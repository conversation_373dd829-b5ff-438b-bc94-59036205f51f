#pragma once
#include <memory>
#include <mutex>
#include <vector>
#include "endian_buffer.h"

#define ROX_STRM_MAGIC 0xaa55
enum ROX_STRM_MSG_ID_E : uint16_t {
    ROX_STRM_MSG_ID_SETUP       = 0x0001, // Setup message
    ROX_STRM_MSG_ID_SETUP_RESP  = 0x0002, // Response message
    ROX_STRM_MSG_ID_PLAY        = 0x0101, // Play message
    ROX_STRM_MSG_ID_VIDEO_DATA  = 0x0102  // Video data message
};
typedef struct {
    uint16_t magic;// ROX_STRM_MAGIC
    uint16_t version;// 0x0001
    uint16_t msgId;// ROX_STRM_MSG_ID_E
    uint32_t bodySize;

    bool encode(EndianBuffer & eb);
    bool decode(EndianBuffer & eb);
} __attribute__((packed)) RoxStrmHead_t;

typedef struct {
    uint8_t   mime; //1: H264, 2: H265
    uint16_t  width;
    uint16_t  height;
    uint8_t   colorFmt;//1: NV12 2: NV21

    uint32_t  bitrate; // Bitrate in bits per second 4<<20 = 4Mbps
    uint8_t   fps;
    uint8_t   ifrmInterval; // I frame interval in seconds
    uint8_t   bitrateMode; // 1: CBR, 2: VBR

    bool encode(EndianBuffer & eb);
    bool decode(EndianBuffer & eb);
} __attribute__((packed)) RoxStrmSetupResp_t;

typedef struct {
    uint8_t   payloadHeadSize;
    uint8_t   cameraId; //1:前 2:后 3:左 4: 右
    uint8_t   frmType;  //1:I  2:P  3:B  4:sps  5:pps
    uint8_t   evtType;  //0:无事件 1:碰撞  2:靠近
    uint8_t   flags;    // 0:正常数据流  1:流结束 2:xxx
    uint32_t  seq;      //帧序列数（每一路视频单独计数）
    uint64_t  absoluteTs; // Absolute timestamp in milliseconds
    uint64_t  relativeTs; // Relative timestamp in milliseconds
    uint32_t  dataSize; // Size of the video data

    bool encode(EndianBuffer & eb);
    bool decode(EndianBuffer & eb);
} __attribute__((packed)) RoxStrmVideoDataHead_t ;