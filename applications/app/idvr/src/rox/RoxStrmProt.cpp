#include "RoxStrmProt.h"
#include "dbg.h"

bool RoxStrmHead_t::encode(EndianBuffer & eb) {
    try {
        eb << magic;
        eb << version;
        eb << msgId;
        eb << bodySize;
    } catch (const std::exception & e) {
        loge("Error encoding RoxStrmHead_t: {}", e.what());
        return false;
    }
    return true;
}

bool RoxStrmHead_t::decode(EndianBuffer & eb) {
    try {
        uint16_t tmp16;
        uint32_t tmp32;
        eb >> tmp16; // Read magic number
        magic = tmp16;
        if (magic != ROX_STRM_MAGIC) {
            loge("Invalid magic {:x}", tmp16);
            return false; // Invalid magic number
        }
        eb >> tmp16; // Read version
        version = tmp16;
        if (version != 0x0001) {
            loge("Invalid version {:x}", tmp16);
            return false; // Unsupported version
        }
        eb >> tmp16; // Read message ID
        msgId = tmp16;
        eb >> tmp32;
        bodySize = tmp32;

    } catch (const std::exception & e) {
        loge("Error decoding RoxStrmHead_t: {}", e.what());
        return false;
    }
    return true;
}

bool RoxStrmSetupResp_t::encode(EndianBuffer & eb) {
    try {
        eb << mime;
        eb << width;
        eb << height;
        eb << colorFmt;
        eb << bitrate;
        eb << fps;
        eb << ifrmInterval;
        eb << bitrateMode;
    } catch (const std::exception & e) {
        loge("Error encoding RoxStrmSetupResp_t: {}", e.what());
        return false;
    }
    return true;
}

bool RoxStrmSetupResp_t::decode(EndianBuffer & eb) {
    try {
        uint8_t tmp8;
        uint16_t tmp16;
        uint32_t tmp32;
        eb >> tmp8; // Read mime type
        mime = tmp8;
        eb >> tmp16; // Read width
        width = tmp16;
        eb >> tmp16; // Read height
        height = tmp16;
        eb >> tmp8; // Read color format
        colorFmt = tmp8;
        eb >> tmp32; // Read bitrate
        bitrate = tmp32;
        eb >> tmp8; // Read fps
        fps = tmp8;
        eb >> tmp8; // Read iframe interval
        ifrmInterval = tmp8;
        eb >> tmp8; // Read bitrate mode
        bitrateMode = tmp8;
    } catch (const std::exception & e) {
        loge("Error decoding RoxStrmSetupResp_t: {}", e.what());
        return false;
    }
    return true;
}

bool RoxStrmVideoDataHead_t::encode(EndianBuffer & eb) {
    try {
        eb << payloadHeadSize;
        eb << cameraId;
        eb << frmType;
        eb << evtType;
        eb << flags;
        eb << seq;
        eb << absoluteTs;
        eb << relativeTs;
        eb << dataSize;
    } catch (const std::exception & e) {
        loge("Error encoding RoxStrmVideoDataHead_t: {}", e.what());
        return false;
    }
    return true;
}
bool RoxStrmVideoDataHead_t::decode(EndianBuffer & eb) {
    try {
        uint8_t tmp8;
        uint32_t tmp32;
        uint64_t tmp64;
        eb >> tmp8;
        payloadHeadSize = tmp8;
        eb >> tmp8;
        cameraId = tmp8;
        eb >> tmp8;
        frmType = tmp8;
        eb >> tmp8;
        evtType = tmp8;
        eb >> tmp8;
        flags = tmp8;
        eb >> tmp32;
        seq = tmp32;
        eb >> tmp64;
        absoluteTs = tmp64;
        eb >> tmp64;
        relativeTs = tmp64;
        eb >> tmp32;
        dataSize = tmp32;
    } catch (const std::exception & e) {
        loge("Error decoding RoxStrmVideoDataHead_t: {}", e.what());
        return false;
    }
    return true;
}