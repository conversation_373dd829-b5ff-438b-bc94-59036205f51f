#include "RoxStrmServer.h"

RoxStrmServer::RoxStrmServer(const char * serverIp, int32_t serverPort)
    : mServerIp(serverIp ? serverIp : "0.0.0.0")
    , mServerPort(serverPort ? serverPort : 1234) {

}
RoxStrmServer::~RoxStrmServer() {
    running_ = false;
    close(serverFd_);
}
bool RoxStrmServer::Init() {
    if (serverFd_ >= 0) {
        return true; // Already initialized
    }
    serverFd_ = socket(AF_INET, SOCK_STREAM, 0);
    sockaddr_in addr{};
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = inet_addr(mServerIp.c_str());
    addr.sin_port = htons(mServerPort);
    bind(serverFd_, (sockaddr*)&addr, sizeof(addr));

    if (listen(serverFd_, 5) < 0) {
        return false;
    }

    return true;
}
void RoxStrmServer::ioWorker() {
    fd_set readfds;
    prctl(PR_SET_NAME, "RoxStrmServer");
    while(running_) {
        FD_ZERO(&readfds);
        FD_SET(serverFd_, &readfds);
        
        timeval timeout{0, 100000}; // 100ms
        if(select(serverFd_+1, &readfds, nullptr, nullptr, &timeout) > 0) {
            sockaddr_in clientAddr{};
            socklen_t len = sizeof(clientAddr);
            int clientFd = accept(serverFd_, (sockaddr*)&clientAddr, &len);
            if(clientFd > 0) {
                int flags = fcntl(clientFd, F_GETFL, 0);
                fcntl(clientFd, F_SETFL, flags | O_NONBLOCK);
                {
                    std::lock_guard<std::mutex> lock(mClientsMtx);
                    mClients[clientFd] = 0; // 0 for active connection
                }
                std::thread(&RoxStrmServer::handleClient, this, clientFd).detach();
            }
        }
    }
}
void RoxStrmServer::handleClient(int clientFd) {
    char head[10];
    std::vector<char> buffer;
    std::string thdName = "RoxStrmCli" + std::to_string(clientFd);
    prctl(PR_SET_NAME, thdName.c_str());

    while(running_) {
        ssize_t bytes = recv(clientFd, &head, sizeof(head), MSG_DONTWAIT);
        if(bytes <= 0) {
            if(errno != EAGAIN && errno != EWOULDBLOCK) break;
            continue;
        } else {
            buffer.insert(buffer.end(), head, head + bytes);
        }
        
        while (buffer.size() >= sizeof(head)) {
            RoxStrmHead_t roxStrmHead;
            EndianBuffer eb(head, sizeof(head));
            if(!roxStrmHead.decode(eb)) {
                loge("Failed to decode RoxStrmHead\n");
                buffer.erase(buffer.begin());
                continue;
            } else {
                buffer.erase(buffer.begin(), buffer.begin() + sizeof(head));
                switch(roxStrmHead.msgId) {
                    case ROX_STRM_MSG_ID_SETUP: {
                        logd("Received setup message from client {}\n", clientFd);
                        // Handle setup message, e.g., send response
                        char setupResp[1024];
                        EndianBuffer ebResp(setupResp, sizeof(setupResp));
                        RoxStrmHead_t respHead;
                        respHead.magic = ROX_STRM_MAGIC;
                        respHead.version = 0x0001;
                        respHead.msgId = ROX_STRM_MSG_ID_SETUP_RESP;
                        respHead.bodySize = 13; // Size of RoxStrmSetupResp_t

                        RoxStrmSetupResp_t setupRespData;
                        setupRespData.mime = 1; // H264
                        setupRespData.width = 1920;
                        setupRespData.height = 1080;
                        setupRespData.colorFmt = 1; // NV12
                        setupRespData.bitrate = 4000000; // 4Mbps
                        setupRespData.fps = 30; // 30 FPS
                        setupRespData.ifrmInterval = 1; // I frame interval in seconds
                        setupRespData.bitrateMode = 2; // CBR

                        if(!respHead.encode(ebResp)) {
                            loge("Failed to encode RoxStrmHead for response\n");
                            continue;
                        } else if (setupRespData.encode(ebResp)) {
                            std::lock_guard<std::mutex> lock(mClientsMtx);
                            mClients[clientFd] = ROX_STRM_MSG_ID_SETUP; // Mark as setup
                            mClientsSeq[clientFd] = std::map<uint8_t, uint32_t>(); // Initialize sequence map
                            send(clientFd, ebResp.dptr(), ebResp.length(), 0);
                        } else {
                            loge("Failed to encode RoxStrmSetupResp_t for response\n");
                        }
                        break;
                    }
                    case ROX_STRM_MSG_ID_PLAY: {
                        logd("Client {} requested play\n", clientFd);
                        std::lock_guard<std::mutex> lock(mClientsMtx);
                        mClients[clientFd] = ROX_STRM_MSG_ID_PLAY; // Mark as playing
                        break;
                    }
                    default: {
                        auto msgId = roxStrmHead.msgId;
                        loge("Unknown message ID {} from client {}\n", msgId, clientFd);
                    }
                }   
            }
        }
    }
    {
        std::lock_guard<std::mutex> lock(mClientsMtx);
        mClients.erase(clientFd);
        mClientsSeq.erase(clientFd);
    }
    close(clientFd); // 清理连接
}
static int32_t H264frmType(unsigned char nal)
{
    int32_t type = nal & 0x1f;
    switch(type) {
        case 5:
            return 1; //iframe
        case 1:
            return 2; // pb frame
        case 7:
            return 4; // sps
        case 8:
            return 5; // pps
        default:
            return 0; // Unknown frame type 
    }
    return 0;
}

#define NALU_INF_START_CODE  0x01000000
void RoxStrmServer::DivideVframes(int32_t clientfd, int8_t chn, int64_t ts, const uint8_t *data, size_t size, std::map<const uint8_t * /*frame start offset*/, RoxStrmVideoDataHead_t> & vframes) {
    // Implementation for dividing SPS, PPS, and I-frame
    size_t idx = 0, start = 0;
    do {
        while (idx < (size - 4)) {
            if (*((uint32_t*)(data + idx)) == NALU_INF_START_CODE) {
                start = idx;
                idx += 4;
                break;
            } else {
                idx++;
            }
        }
        if (idx >= size - 4) {
            break;
        }
        if (data[idx] == 0x41 || data[idx] == 0x67 || data[idx] == 0x68 || data[idx] == 0x65) { // SPS PPS I-FRMAE
            if (data[idx] == 0x41 || data[idx] == 0x65) {
                idx = size;
            } else {
                idx++;
                while (idx < size && *(uint32_t*)(data + idx) != NALU_INF_START_CODE) {
                    idx++;
                }
            }
            RoxStrmVideoDataHead_t frame;
            frame.payloadHeadSize = sizeof(RoxStrmVideoDataHead_t) - 1; // Size of the header excluding the payload size
            frame.cameraId = chn;
            frame.frmType = H264frmType(data[start + 4]); // SPS frame type
            frame.evtType = 0; // No event
            frame.flags = 0; // Normal data stream
            frame.seq = mClientsSeq[clientfd][chn]++; // Sequence number, could be managed separately
            frame.absoluteTs = ts; // Absolute timestamp in milliseconds
            frame.relativeTs = minieye::system::GetUptimeMs(); // Relative timestamp in milliseconds
            frame.dataSize = idx - start; // Size of the video data
            vframes[data + start] = frame;
            start = idx;
            if (data[idx] == 0x41) {
                break;
            }
        }
    } while (idx < size - 4);
}

void RoxStrmServer::OnDataMsgRcved(const std::shared_ptr<CodecOut> & spDsMsg) {
    if (spDsMsg && spDsMsg->ptr) {
        logd("Received CodecOut: size={}, type={}, chn={}, ts={}\n",
             spDsMsg->size, spDsMsg->type, spDsMsg->chn, spDsMsg->ts);
        std::lock_guard<std::mutex> lock(mClientsMtx);
        for (const auto & client : mClients) {
            if (client.second != ROX_STRM_MSG_ID_PLAY) {
                continue;
            } else {
                std::map<const uint8_t *, RoxStrmVideoDataHead_t> vframes;
                DivideVframes(client.first, spDsMsg->chn, spDsMsg->ts, static_cast<uint8_t*>(spDsMsg->ptr), spDsMsg->size, vframes);
                for (auto & r : vframes) {
                    RoxStrmHead_t respHead;
                    respHead.magic = ROX_STRM_MAGIC;
                    respHead.version = 0x0001;
                    respHead.msgId = ROX_STRM_MSG_ID_VIDEO_DATA;
                    respHead.bodySize = sizeof(RoxStrmVideoDataHead_t) + spDsMsg->size; // Size of the video data head + data

                    auto & videoDataHead = r.second;
                    char videoDataBuffer[1024]; // Adjust size as needed
                    EndianBuffer eb(videoDataBuffer, sizeof(videoDataBuffer));
                    if (!respHead.encode(eb)) {
                        loge("Failed to encode RoxStrmHead for client {}\n", client.first);
                        continue;
                    } else if (!videoDataHead.encode(eb)) {
                        loge("Failed to encode videoDataHead for client {}\n", client.first);
                        continue;
                    } else if (send(client.first, eb.dptr(), eb.length(), 0) < 0) {
                        loge("Failed to send videoDataHead to client {}\n", client.first);
                        mClients.erase(client.first); // Remove client on failure
                        mClientsSeq.erase(client.first);
                        break;
                    } else if (send(client.first, r.first, videoDataHead.dataSize, 0) < 0) {
                        loge("Failed to send data to client {}\n", client.first);
                        mClients.erase(client.first); // Remove client on failure
                        mClientsSeq.erase(client.first);
                        break;
                    } else {
                        logd("Sent data to client {}\n", client.first);
                    }
                }
            }
        }
    }
}