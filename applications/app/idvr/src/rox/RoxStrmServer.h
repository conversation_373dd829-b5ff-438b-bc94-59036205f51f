#pragma once
#include <memory>
#include <mutex>
#include "CodecOut.h"
#include "DataSrc.h"
#include "DvrService.h"
#include "RoxStrmProt.h"

class RoxStrmServer
    : public DataObserver<CodecOut> {
  public:
    RoxStrmServer(const char * serverIp, int32_t serverPort);
    ~RoxStrmServer();
    bool Init();
    bool Start() {
        if (running_) {
            return true; // Already running
        }
        running_ = true;
        serverThread_ = std::thread(&RoxStrmServer::ioWorker, this);
        return true;
    }
  protected:
    virtual void OnDataMsgRcved(const std::shared_ptr<CodecOut> & spDsMsg) override;
  private:
    void handleClient(int clientFd);
    void ioWorker();
    void DivideVframes(int32_t clientfd, int8_t chn, int64_t ts, const uint8_t *data, size_t size, std::map<const uint8_t * /*frame start offset*/, RoxStrmVideoDataHead_t> & vframes);
  private:
    std::string mServerIp = "0.0.0.0";
    int32_t mServerPort = 1234;

    int serverFd_ = -1;
    std::atomic<bool> running_{false};
    std::thread serverThread_;

    std::mutex mClientsMtx;
    std::map<int32_t/*socket*/, int32_t/*stat*/> mClients;
    std::map<int32_t/*socket*/, std::map<uint8_t/*channel*/, uint32_t/*seq*/>> mClientsSeq;

};