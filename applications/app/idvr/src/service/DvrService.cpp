#include <vector>

#include "Venc.h"
//#include "Timer.h"
#include "Camera.h"
#include "RoxStrmServer.h"
#include "DvrService.h"

class DvrService
  : public std::enable_shared_from_this<DvrService>
{
  public:
    static std::shared_ptr<DvrService> GetInstance() {
        static std::mutex mInstanceMtx;
        static std::shared_ptr<DvrService> mspInstance = nullptr;
        std::lock_guard<std::mutex> lock(mInstanceMtx);
        if (mspInstance == nullptr) {
            mspInstance = std::shared_ptr<DvrService>(new DvrService());
        }
        return mspInstance;
    }

    bool Init(int32_t camNum, const VencParam_t & vp, const char * localIp, int32_t localPort);
    bool Start() {
        std::lock_guard<std::mutex> lock(mMtx);
        for (auto & it : mChnVencs) {
            if (it.second != nullptr) {
                if (!it.second->Start()) {
                    loge("venc {} start fail!\n", it.first);
                    return false;
                } else if (mCamsMap[it.first] != nullptr) {
                    if (!mCamsMap[it.first]->Start()) {
                        loge("cam {} start fail!\n", it.first);
                        return false;
                    }
                } else {
                    loge("cam {} is null!\n", it.first);
                    return false;
                }
            } else {
                loge("venc {} is null!\n", it.first);
                return false;
            }
        }
        return true;
    }
    int32_t Stop() {
        std::lock_guard<std::mutex> lock(mMtx);
        // Stop all cameras and encoders
        for (auto & it : mCamsMap) {
            if (it.second != nullptr) {
                it.second->Stop();
            }
        }
        for (auto & it : mChnVencs) {
            if (it.second != nullptr) {
                it.second->Stop();
            }
        }
        return 0;
    }
    int32_t PushFrame(const std::shared_ptr<CamImgInfo_t> & spImgInfo) {
        std::lock_guard<std::mutex> lock(mMtx);
        auto it = mCamsMap.find(spImgInfo->camera_id);
        if (it != mCamsMap.end() && it->second != nullptr) {
            it->second->OnCamData(spImgInfo);
            return 0;
        }
        return -11;
    }
    ~DvrService() {
        std::lock_guard<std::mutex> lock(mMtx);
        mCamsMap.clear();
        mChnVencs.clear();
    }    
  private:
    DvrService() {

    }

  private:
    std::mutex mMtx;
    std::shared_ptr<RoxStrmServer>     mspRoxStrmServer = nullptr;

    std::map<int32_t, std::shared_ptr<Camera<CamImgInfo_t>>>  mCamsMap;
    std::map<int32_t, std::shared_ptr<Venc>>    mChnVencs;
};

bool DvrService::Init(int32_t camNum, const VencParam_t & vp, const char * localIp, int32_t localPort) {
    std::lock_guard<std::mutex> lock(mMtx);
    if (camNum <= 0) {
        loge("camNum is invalid {}, must be greater than 0!\n", camNum);
        return false;
    }
    std::shared_ptr<RoxStrmServer> mspRoxStrmServer = std::make_shared<RoxStrmServer>(localIp, localPort);
    if (mspRoxStrmServer == nullptr) {
        loge("RoxStrmServer::GetInstance() fail!\n");
        return false;
    }
    if (!mspRoxStrmServer->Init()) {
        loge("RoxStrmServer init fail!\n");
        return false;
    }
    if (!mspRoxStrmServer->Start()) {
        loge("RoxStrmServer start fail!\n");
        return false;
    }
    for (int32_t i = 0; i < camNum; i++) {
        std::shared_ptr<Camera<CamImgInfo_t>> camera = std::make_shared<Camera<CamImgInfo_t>>(i);
        if (camera == nullptr) {
            loge("create camera {} fail!\n", i);
            return false;
        }
        if (!camera->Init()) {
            loge("camera {} init fail!\n", i);
            return false;
        }
        
        std::string vencName = "venc" + std::to_string(i);
        mChnVencs[i] = std::make_shared<Venc>(vencName.c_str(), i, vp);
        if (mChnVencs[i] != nullptr) {
            mChnVencs[i]->Init();
            mChnVencs[i]->AddDataObserver(vencName, mspRoxStrmServer);

            mCamsMap[i] = camera;
            std::string name = "cam" + std::to_string(i);
            camera->AddDataObserver(name, mChnVencs[i]);

            logd("create chn {} success!\n", i);
        } else {
            loge("create chn {} fail!\n", i);
            return false;
        }
    }
    return true;
}

bool DvrServiceInit(int32_t camNum, const VencParam_t * pVP, const char * localIp, int32_t localPort) {
    signal(SIGPIPE, SIG_IGN);
    std::shared_ptr<DvrService> dvrService = DvrService::GetInstance();
    if (dvrService == nullptr) {
        loge("DvrService::GetInstance() fail!\n");
        return false;
    }
    const VencParam_t defaultVP = {
        .outResl = {
            .width = 1920,
            .height = 1080
        },
        .codecType = CODEC_TYPE_H264_ENC,
        .bitrate = 4000, // kbps
        .crop = 0, // 0 resize, 1 crop
        .fps = 30
    };
    const VencParam_t & vp = (pVP != nullptr) ? *pVP : defaultVP;
    return dvrService->Init(camNum, vp, localIp, localPort);
}

int32_t DvrServiceStart() {
    std::shared_ptr<DvrService> dvrService = DvrService::GetInstance();
    if (dvrService == nullptr) {
        loge("DvrService::GetInstance() fail!\n");
        return false;
    }
    return dvrService->Start() ? 0 : -1;
}

int32_t DvrServiceStop() {
    std::shared_ptr<DvrService> dvrService = DvrService::GetInstance();
    if (dvrService == nullptr) {
        loge("DvrService::GetInstance() fail!\n");
        return -1;
    }
    
    return dvrService->Stop();
}

int32_t DvrServicePushFrame(const CamImgInfo_t & imgInfo) {
    std::shared_ptr<DvrService> dvrService = DvrService::GetInstance();
    if (dvrService == nullptr) {
        loge("DvrService::GetInstance() fail!\n");
        return -1;
    }
    std::shared_ptr<CamImgInfo_t> imgInfoPtr = std::make_shared<CamImgInfo_t>(imgInfo);
    if (imgInfoPtr == nullptr) {
        loge("make_shared<CamImgInfo_t> fail!\n");
        return -2;
    }
    return dvrService->PushFrame(imgInfoPtr);
}