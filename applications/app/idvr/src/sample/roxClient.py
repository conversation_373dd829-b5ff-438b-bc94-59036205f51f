
import socket
import struct
import binascii
from enum import IntEnum
def print_hex(data, limit=None):
    """
          打印数据的十六进制表示
    :param data: 字节数据(bytes/bytearray)
    :param limit: 最多打印的字节数(None表示全部打印)
    """
    if limit is not None and limit > 0:
        data = data[:limit]
    hex_str = ' '.join(f'{b:02x}' for b in data)
    print(f"Hex data ({len(data)} bytes): {hex_str}")
    #print(f"Hexdump:\n{binascii.hexlify(data).decode('ascii')}")

class ROX_STRM_MSG_ID_E(IntEnum):
    SETUP = 0x0001
    SETUP_RESP = 0x0002
    PLAY = 0x0101
    VIDEO_DATA = 0x0102

class FrameType(IntEnum):
    I_FRAME = 1
    P_FRAME = 2
    B_FRAME = 3
    SPS = 4
    PPS = 5

def build_header(msg_id, body_size=0):
    return struct.pack('>HHHI', 0xAA55, 0x0001, msg_id, body_size)

def parse_header(data):
    magic, version, msg_id, body_size = struct.unpack('>HHHI', data)
    if magic != 0xAA55 or version != 0x0001:
        print_hex(data)
        raise ValueError("Invalid header")
    return msg_id, body_size

def parse_setup_resp(data):
    print_hex(data)
    return struct.unpack('>BHHBIBBB', data)

def parse_video_head(data):
    try:
        return struct.unpack('>BBBBBIQQI', data)
    except (ConnectionResetError, struct.error) as e:
        print_hex(data)
        print(f"Error: {e}")

class VideoStreamClient:
    def __init__(self, host, port):
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.connect((host, port))
        
    def setup(self):
        self.sock.send(build_header(ROX_STRM_MSG_ID_E.SETUP))
        resp = self.sock.recv(1024)
        print_hex(resp)
        msg_id, body_size = parse_header(resp[:10])
        if msg_id == ROX_STRM_MSG_ID_E.SETUP_RESP:
            return parse_setup_resp(resp[10:10+body_size])
        raise RuntimeError("Setup failed")

    def play(self):
        self.sock.send(build_header(ROX_STRM_MSG_ID_E.PLAY))

    def recvData(self, data, want):
        try:
            # Receive frame data
            received = 0
            while received < want:
                chunk = self.sock.recv(min(4096, want - received))
                data+=chunk
                received += len(chunk)
                #print(f"chunk_size={received}")
            return data
        except (ConnectionResetError, struct.error) as e:
            print(f"Error: {e}")

    def receive_stream(self, output_prefix):
        """
        接收视频流数据，按通道分离保存为H.264文件
        参数:
            output_prefix: 输出文件前缀，实际文件名为 {output_prefix}_{ch}.h264
        """
        # 创建通道文件句柄字典 {ch: file_handle}
        file_handles = {}
        
        try:
            while True:
                try:
                    # 接收消息头 (10字节)
                    header = self.recvData(b'', 10)
                    if not header:
                        print(f"空头信息，连接可能已关闭")
                        break
                        
                    # 解析消息头
                    msg_id, body_size = parse_header(header)
                    if msg_id != ROX_STRM_MSG_ID_E.VIDEO_DATA:
                        # 跳过非视频数据消息
                        self.recvData(b'', body_size)  # 丢弃消息体
                        continue
                    
                    # 接收视频头 (29字节)
                    video_head = self.recvData(b'', 29)
                    head_data = parse_video_head(video_head)
                    
                    # 提取关键信息
                    ch = head_data[1]
                    data_size = head_data[8]
                    frame_type = FrameType(head_data[2]).name
                    seq_num = head_data[5]
                    
                    # 打印调试信息
                    print(f"通道 {ch}: 帧大小={data_size}字节, 类型={frame_type}, 序列号={seq_num}")
                    
                    # 按需创建通道文件
                    if ch not in file_handles:
                        filename = f"{output_prefix}_{ch}.h264"
                        file_handles[ch] = open(filename, 'wb')
                        print(f"创建通道 {ch} 的输出文件: {filename}")
                    
                    # 接收帧数据
                    frame_data = self.recvData(b'', data_size)
                    
                    # 写入到对应通道的文件
                    file_handles[ch].write(frame_data)
                    
                    # 调试输出：打印关键帧的前64字节
                    if frame_type == "KEY_FRAME":
                        print_hex(frame_data[:64], 64)
                        print(f"通道 {ch} 关键帧: 大小={len(frame_data)} 序列号={seq_num}")
                        
                except (ConnectionResetError, struct.error) as e:
                    print(f"接收错误: {e}")
                    break
                    
        finally:
            # 确保所有文件都正确关闭
            for ch, f in file_handles.items():
                f.close()
                print(f"关闭通道 {ch} 的输出文件")
            print("所有文件已关闭")

    def close(self):
        self.sock.close()

if __name__ == '__main__':
    client = VideoStreamClient('192.168.98.233', 1234)
    try:
        setup_resp = client.setup()
        print(f"Setup response: {setup_resp}")
        client.play()
        client.receive_stream('ch')
    finally:
        client.close()

