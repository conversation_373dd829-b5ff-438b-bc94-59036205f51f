import("//build/minieye/minieye.gni")

minieye_group("imu_group") {
    deps = [
        ":imu",
        ":imu_sample",
        ":imu_test",
        ":imu_demo",
    ]

}

app_cmm_dir = rebase_path("//applications/app/common/")

minieye_shared_library("imu") {
    sources = [
        "${app_cmm_dir}/timehal/src/minieye_time_hal.cpp",
        "src/ImuDevice.cpp",
        "src/TtyDevice.cpp",
        "src/GpioCtrl.cpp",
    ]

    include_dirs = [
        "${app_cmm_dir}/timehal/include",
        "include",
        "src",
    ]
    cflags_cc = [
        "-Wall",
        "-Wno-deprecated-declarations",
    ]
    ldflags = [
        "-lhbtimesynchal",
    ]


    deps = [
        "//middleware/mlog:libmlog",
        "//middleware/system/core/libcppbase:cppbase",
    ]
}

minieye_executable("imu_sample") {
    sources = [
        "sample/main.cpp",
    ]

    include_dirs = [
        "include",
    ]

    deps = [
        ":imu",
    ]
}

minieye_executable("imu_test") {
    sources = [
        "test/main.cpp",
    ]

    include_dirs = [
        "include",
    ]

    ldflags = [
        "-lgtest",
        "-lgtest_main",
    ]

    deps = [
        ":imu",
    ]
}

minieye_executable("imu_demo") {
    sources = [
        "test/demo.cpp",
    ]

    include_dirs = [
        "include",
    ]

    deps = [
        ":imu",
    ]
}

minieye_prebuilt_etc("imu_tools") {
    source = "ota/bin/imu_tools"
    relative_install_dir = "bin"
}

#minieye_prebuilt_etc("firmware") {
#    source = "ota/firmware/NAV3120SAA-VB1.02.08.00.00.00-2b9eedf.pkg"
#    relative_install_dir = "bin"
#}