/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : xujiesen
 * Date         : 2025-09-02
 */
#pragma once
#include <string>
#include <unordered_map>
#include "mlog/Logging.h"

#include "CppBase.h"

namespace minieye {
namespace imu {

struct ImuDataMessage;

using ImuDataCallback = std::function<void(const char *data, int32_t dataLen)>;

class TtyDevice : public minieye::system::Thread {
 public:
    TtyDevice();
    ~TtyDevice();
    bool Init(const char *dev, uint32_t baud);
    bool AddDataCb(const std::string name, ImuDataCallback cb);
    bool Start(const std::string bufTag);
    int32_t Send(const char *data, int32_t size);
    uint64_t GetLastRcvSec() const;

 protected:
    virtual bool InitTty();
    // virtual void OnMsgRcvd(const std::shared_ptr<DsMsg> & msg) override;

 private:
    static void threadBody(TtyDevice *dev);
    bool setBaudrate(int32_t fd, int32_t speed);

 private:
    std::shared_ptr<minieye::system::Thread> mRecvThread;
    static minieye::system::BufferQueue<std::shared_ptr<std::vector<char>>> mBufQueue;
    std::mutex mMtx;
    std::mutex mSndMtx;
    uint64_t mLastRcvSecs;

    bool mbInit = false;
    std::unordered_map<std::string, ImuDataCallback> mDataCbs;
    std::string mDevPath;
    uint32_t mBaud = 0;
    int32_t mFd = -1;
};

}  // namespace imu
}  // namespace minieye
