#include <unistd.h>
#include <string.h>
#include <termios.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include "ImuDevice.h"
#include "TtyDevice.h"

namespace minieye {
namespace imu {

minieye::system::BufferQueue<std::shared_ptr<std::vector<char>>> TtyDevice::mBufQueue;

TtyDevice::TtyDevice()
    : Thread(threadBody, this) {
}

TtyDevice::~TtyDevice() {
    mbInit = false;
    mRecvThread->Stop();
    Thread::Stop();

    Thread::msleep(15);
    if (mFd) {
        close(mFd);
        mFd = -1;
    }
}

bool TtyDevice::Init(const char* dev, uint32_t baud) {
    {
        std::lock_guard<std::mutex> lock(mMtx);

        if (mbInit) {
            return true;
        }

        mbInit = true;
        mDevPath = dev;
        mBaud = baud;
    }
    InitTty();
    return true;
}

bool TtyDevice::setBaudrate(int32_t fd, int speed) {
    int speed_arr[] = {B921600, B115200, B57600, B460800, B230400,B38400, B19200, B9600, B4800, B2400, B1200, B600};
    int name_arr[] = {921600, 115200, 57600, 460800, 230400, 38400, 19200, 9600, 4800, 2400, 1200, 600};

    uint32_t i;
    int status;
    struct termios Opt;

    if (fd < 0) {
        MLOG_E("tty {} open failed", mDevPath.c_str());
        return false;
    }

    tcgetattr(fd, &Opt);

    if (tcgetattr(fd, &Opt) != 0) {
        MLOG_E("tcgetattr error");
        return false;
    }

    for (i = 0; i < sizeof(speed_arr) / sizeof(int); i++) {
        if (speed == name_arr[i]) {
            MLOG_E("set mBaudrate = {}", speed);
            tcflush(fd, TCIOFLUSH);
            cfsetispeed(&Opt, speed_arr[i]);
            cfsetospeed(&Opt, speed_arr[i]);
            status = tcsetattr(fd, TCSANOW, &Opt);

            if (status != 0) {
                MLOG_E("tcsetattr error");
            }

            tcflush(fd, TCIOFLUSH);
            MLOG_I("set speed {} ok.", speed);
            return true;
        }
    }

    tcflush(fd, TCIOFLUSH);
    MLOG_E("set speed {} fail.", speed);
    return false;
}

bool TtyDevice::InitTty() {
    std::lock_guard<std::mutex> lock(mMtx);
    int32_t fd = open(mDevPath.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);

    if (fd >= 0) {
        struct termios tty;
        memset(&tty, 0, sizeof tty);

        if (tcgetattr(fd, &tty) != 0) {
            MLOG_E("tcgetattr failed {}", strerror(errno));
            goto _fail_;
        }

        cfmakeraw(&tty);

        if (tcsetattr(fd, TCSANOW, &tty) != 0) {
            MLOG_E("tcsetattr failed {}", strerror(errno));
            goto _fail_;
        }

        if (setBaudrate(fd, mBaud)) {
            mFd = fd;
            return true;
        }
    }

_fail_:

    if (fd >= 0) {
        close(fd);
    }

    return false;
}

int32_t TtyDevice::Send(const char* data, int32_t size) {
    if (mFd < 0) {
        MLOG_E("Invalid tty fd!");
        return 0;
    }
    std::lock_guard<std::mutex> lg(mSndMtx);

    return write(mFd, data, size);
}

uint64_t TtyDevice::GetLastRcvSec() const {
    return mLastRcvSecs;
}

bool TtyDevice::AddDataCb(const std::string name, ImuDataCallback cb) {
    std::lock_guard<std::mutex> lock(mMtx);

    if (cb != nullptr) {
        mDataCbs[name] = cb;
        return true;
    }

    return false;
}

bool TtyDevice::Start(const std::string bufTag) {
    mRecvThread = std::make_shared<minieye::system::Thread>([this, bufTag]() {
        std::shared_ptr<std::vector<char>> sp = nullptr;
        mLastRcvSecs = minieye::system::GetUptimeSec();
        while (!mRecvThread->IsExiting()) {
            if (mBufQueue.wait(sp)) {
                mDataCbs[bufTag](sp->data(), sp->size());
                mLastRcvSecs = minieye::system::GetUptimeSec();
            }
        }
        return;
    });
    return mRecvThread->Start() && minieye::system::Thread::Start();
}

void TtyDevice::threadBody(TtyDevice* dev) {
    const char* devName = strrchr(dev->mDevPath.c_str(), '/');

    if (devName) {
        devName++;

    } else {
        devName = "ttyDev";
    }

    prctl(PR_SET_NAME, devName);

    if (!dev->mbInit) {
        MLOG_E("Not init!!!");
        return;
    }

    while (!dev->IsExiting()) {
        if (dev->mFd < 0) {
            if (!dev->InitTty()) {
                msleep(8000);
                MLOG_E("initTty fail! {}, {}", dev->mDevPath.c_str(), dev->mBaud);
            }
        }

        if (dev->mFd >= 0) {
            struct timeval timeout = {0, 10000};
            fd_set rfds;
            FD_ZERO(&rfds);
            FD_SET(dev->mFd, &rfds);
            // MLOG_E("fd {}", dev->mFd);
            int32_t ret = select(dev->mFd + 1, &rfds, nullptr, nullptr, &timeout);

            if (ret < 0) {
                MLOG_E("selected failed {}", strerror(errno));
                close(dev->mFd);
                dev->mFd = -1;

            } else if (ret == 0) {
                // MLOG_E("timeout! {}", dev->mFd);

            } else {
                std::shared_ptr<std::vector<char>> sp = std::make_shared<std::vector<char>>(4 << 10);
                int rl = 0;

                if (0 < (rl = read(dev->mFd, sp->data(), 4 << 10))) {
                    // MLOG_E("read {}", rl);
                    sp->resize(rl);
                    mBufQueue.push(sp);
                }
            }
        }
    }
}

}  // namespace imu
}  // namespace minieye