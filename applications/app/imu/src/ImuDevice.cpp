/*
 * Copyright [2023] MINIEYE
 * Descripttion : IMU接口实现
 * Author       : xu<PERSON>esen
 * Date         : 2025-04-02
 */

#include "ImuDevice.h"
#include <iostream>
#include <chrono>
#include <cstring>
#include <cstdio>

#include "GpioCtrl.h"
#include "TtyDevice.h"
#include "hobot_clock_hal.h"

#define IMU_PWR_GPIO_IDX    (478)
#define IMU_DEVICE_BAUDRATE (230400)
#define IMU_DEVICE_NAME     "/dev/ttyS2"
#define IMU_BUFTAG          "imuData"

#define IMU_RESET_TIMEOUT_SEC (60ULL)

#define MAX_PPS_PATH_IMU (60)

namespace minieye {
namespace imu {

template<int ExpireTime = 60, int NoDataReportTime = 10, int ReportInterval = ExpireTime>
class DataStatics {
 public:
    DataStatics()
        : mTotLens(0)
        , mLastDataTime(0) {
    }
    void start() {
        if (mIsRunning) {
            return;
        }
        mIsRunning = true;
        mDataStaticsThread = std::thread([this]() {
            while (mIsRunning) {
                {
                    std::lock_guard<std::mutex>lg(mMtx);
                    cleanExpireData();
                    // 错误统计
                    for (auto& [faultType, faultInfo] : mFaultMap) {
                        const auto& [faultCnt, faultTime] = faultInfo;
                        if (minieye::system::GetUptimeSec() - faultTime < ExpireTime &&
                            minieye::system::GetUptimeSec() - mLastReportTimeMap[faultType] > ReportInterval) {
                            MLOG_W("{}", getFaultReport());
                            mLastReportTimeMap[faultType] = minieye::system::GetUptimeSec();
                        }
                    }
                    // 无数据统计
                    if (minieye::system::GetUptimeSec() - mLastDataTime > NoDataReportTime &&
                        minieye::system::GetUptimeSec() - mLastReportTimeMap["noData"] > ReportInterval) {
                        MLOG_W("{}", getNoDataReport());
                        mLastReportTimeMap["noData"] = minieye::system::GetUptimeSec();
                    }
                    // 统计信息
                    if (minieye::system::GetUptimeSec() - mLastReportTimeMap["statistics"] > ReportInterval) {
                        MLOG_I("{}", getStatistics());
                        mLastReportTimeMap["statistics"] = minieye::system::GetUptimeSec();
                    }
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
            MLOG_I("mDataStaticsThread exiting!!");
        });
    }

    void addData(uint32_t len) {
        std::lock_guard<std::mutex> lg(mMtx);
        mLastDataTime = minieye::system::GetUptimeSec();
        mTotLens += len;
        mDataQueue.emplace_back(std::make_pair(mLastDataTime, len));
    }

    void addFault(int32_t faultType) {
        std::string faultStr = "fault_type = " + std::to_string(faultType);
        addFault(faultStr);
    }

    void addFault(std::string faultType) {
        std::lock_guard<std::mutex> lg(mMtx);
        if (mFaultMap.find(faultType) != mFaultMap.end()) {
            mFaultMap[faultType] = std::make_pair(mFaultMap[faultType].first + 1, minieye::system::GetUptimeSec());
        } else {
            mFaultMap[faultType] = std::make_pair(1, minieye::system::GetUptimeSec());
        }
    }

    void reset() {
        {
            std::lock_guard<std::mutex> lg(mMtx);
            mIsRunning = false;
        }
        if (mDataStaticsThread.joinable()) {
            mDataStaticsThread.join();
        }
        {
            std::lock_guard<std::mutex> lg(mMtx);
            mLastReportTimeMap.clear();
            mDataQueue.clear();
            mFaultMap.clear();
            mTotLens = 0;
            mLastDataTime = 0;
        }
    }

    std::string getStatistics() {
        std::string ret = "Last " + std::to_string(ExpireTime) + "s: dataCount: " + std::to_string(mDataQueue.size()) +
                          ", totLens: " + std::to_string(mTotLens) + ", lastDataTime: " + std::to_string(mLastDataTime);
        return ret;
    }

    std::string getNoDataReport() {
        auto noDataTime = minieye::system::GetUptimeSec() - mLastDataTime;
        return "No data received for " + std::to_string(noDataTime) + "s";
    }

    std::string getFaultReport() {
        std::string ret = "FaultList:";
        for (auto& it : mFaultMap) {
            ret += '\n' + it.first + "cnt: " + std::to_string(it.second.first) +
                   " last report at: " + std::to_string(it.second.second);
        }
        return ret;
    }

 private:
    void cleanExpireData() {
        auto curTime = minieye::system::GetUptimeSec();
        if (curTime > ExpireTime) {
            auto expireTime = curTime - ExpireTime;
            auto it = mDataQueue.begin();
            while (it != mDataQueue.end() && it->first < expireTime) {
                mTotLens -= it->second;
                it++;
            }
            mDataQueue.erase(mDataQueue.begin(), it);
        }
    }

    std::deque<std::pair<uint64_t /*timestamp*/, uint32_t /*len*/>> mDataQueue;
    std::map<std::string, std::pair<uint64_t /*fault cnt*/, uint64_t /*timestamp*/>> mFaultMap;
    uint64_t mTotLens;
    uint64_t mLastDataTime;

    std::mutex mMtx;
    std::map<std::string, uint64_t> mLastReportTimeMap;
    std::thread mDataStaticsThread;
    std::atomic<bool> mIsRunning = false;
};

static DataStatics dataStatics;
static ImuDevice* instance = nullptr;

static bool SetupImu() {
    static bool ret = false;
    if (ret) {
        return true;
    }
    ret = true;
    ret &= GpioCtrl::SetGpioDirection(IMU_PWR_GPIO_IDX, "out");
    ret &= GpioCtrl::SetGpio(IMU_PWR_GPIO_IDX, "0");
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    ret &= GpioCtrl::SetGpio(IMU_PWR_GPIO_IDX, "1");
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    return ret;
}

/*find pps path with pps name*/
static int find_pps_path(char* pps_path, const char* pps_find_name) {
    int fd;
    int ret;
    char current_pps_name[MAX_PPS_PATH_IMU];
    MLOG_I("use pps name: {}", pps_find_name);

    if ((pps_path == NULL) || (pps_find_name == NULL)) {
        return -EINVAL;
    }

    for (int i = 0; i < 5; i++) {
        memset(current_pps_name, 0, sizeof(current_pps_name));
        memset(pps_path, 0, MAX_PPS_PATH_IMU);

        snprintf(pps_path, MAX_PPS_PATH_IMU, "/sys/class/pps/pps%d/name", i);

        fd = open(pps_path, O_RDONLY);
        if (fd < 0) {
            return -1;
        }

        ret = read(fd, current_pps_name, sizeof(current_pps_name));
        if (ret < 0) {
            close(fd);
            return -1;
        }

        if (strstr(current_pps_name, pps_find_name) != NULL) {
            snprintf(pps_path, MAX_PPS_PATH_IMU, "/dev/pps%d", i);
            close(fd);
            return 0;
        }

        close(fd);
    }

    return -1;
}

static uint16_t Crc16(const uint8_t* data, int32_t length) {
    // uint16_t crc = 0xFFFF; // 初始值为0xFFFF
    int i = 0;
    uint16_t wCRCin = 0xffff;
    uint16_t wCPoly = 0x1021;
    uint8_t wChar = 0;

    while ((length--) > 0) {
        wChar = data[i++];
        wCRCin ^= (uint16_t)(wChar << 8);
        for (int j = 0; j < 8; j++) {
            if ((wCRCin & 0x8000) != 0) {
                wCRCin = (uint16_t)((wCRCin << 1) ^ wCPoly);
            } else {
                wCRCin = (uint16_t)(wCRCin << 1);
            }
        }
    }
    return wCRCin;
}

struct ImuData {
    uint8_t r1[36];
    uint64_t faultStatus : 48;
    uint8_t r2[20];
    uint16_t crc;
} __attribute__((packed));

static int chkFaultStatus(char const* data, int32_t len) {
    if (len < 64) {
        return -1;
    }
    constexpr char MAGIC[] = "\xBD\x64\x8B\x00";
    auto i = strstr(data, MAGIC);
    if (i == nullptr || i + 64 > data + len) {
        return -1;
    }
    ImuData* pData = (ImuData*)i;

    // crc校验
    if (Crc16((uint8_t*)i, 62) != pData->crc) {
        return i - data + 4;
    }

    if (pData->faultStatus != 0) {
        for (int i = 0; i < 48; i++) {
            if (pData->faultStatus & (1ULL << i)) {
                dataStatics.addFault(i);
            }
        }
    }

    return i - data + 64;
}

bool ImuDevice::Init(ImuDataCallback callback) {
    if (mIsRunning) {
        return mTtyDevice->AddDataCb(IMU_BUFTAG, [callback](char const* data, int32_t len) {
            callback(data, len);
            dataStatics.addData(len);
            auto solved = 0;
            while ((solved = chkFaultStatus(data, len)) >= 0) {
                len -= solved;
            }
        });
    }
    mTtyDevice = std::make_shared<TtyDevice>();
    if (mTtyDevice == nullptr) {
        return false;
    }
    bool ret = SetupImu();
    ret = ret && mTtyDevice->Init(IMU_DEVICE_NAME, IMU_DEVICE_BAUDRATE);
    ret = ret && mTtyDevice->AddDataCb(IMU_BUFTAG, [callback](char const* data, int32_t len) {
        callback(data, len);
        chkFaultStatus(data, len);
        dataStatics.addData(len);
    });
    ret = ret && mTtyDevice->Start(IMU_BUFTAG);
    if (ret) {
        mIsRunning = true;
        mThread = std::thread([this]() { threadBody(); });
        dataStatics.start();
    }
    return ret;
}

bool ImuDevice::WriteData(const char* data, int32_t dataLen) {
    if (!mIsRunning || mTtyDevice == nullptr) {
        return false;
    }
    return mTtyDevice->Send(data, dataLen);
}

bool ImuDevice::Reset() {
    if (!mIsRunning) {
        return false;
    }
    MLOG_I("Resetting imu device");
    if (!GpioCtrl::SetGpio(IMU_PWR_GPIO_IDX, 0)) {
        return false;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    if (!GpioCtrl::SetGpio(IMU_PWR_GPIO_IDX, 1)) {
        return false;
    }
    MLOG_I("Reset imu device finished");
    return true;
}

ImuDevice& ImuDevice::GetInstance() {
    if (instance == nullptr) {
        instance = new ImuDevice();
        minieye::mlog::LogConfig config;
        config.level = minieye::mlog::LogInfo;        // log level
        config.tag = "imu";                           // log tag name
        config.isRemoteLog = true;                    // print to mlogcat or not
        config.isLogFile = false;                     // log file save or not
        config.logDomain = minieye::mlog::DomainApp;  // remote log buffer
        MLOG_INIT(config);
    }
    return *instance;
}

ImuDevice::ImuDevice()
    : mIsRunning(false), mTtyDevice(nullptr) {
}

ImuDevice::~ImuDevice() {
    mIsRunning = false;
    if (mThread.joinable()) {
        mThread.join();
    }
    // GpioCtrl::SetGpio(IMU_PWR_GPIO_IDX, 0);
}

void ImuDevice::threadBody() {
    auto sendRtcTimeToImu = [this](uint32_t sec) {
        uint8_t timeSyncSignal[13] = {0xAB, 0x54,   // Header（固定值）
            0x13, 0x00,                             // 功能码与子功能码（功能码为固定值，子功能码单传感器默认0x00）
            0x00, 0x00,                             // 数据部分长度，必须为0
            0x00, 0x00, 0x00, 0x00,         // 授时Unix时间戳，单位s
            0x00,                                      // 递加序列号（0~255）
            0x00, 0x00                            // CRC16（包括0~10字节）
        };
        static uint8_t seq = 0;
        memcpy(&timeSyncSignal[6], &sec, 4);
        timeSyncSignal[10] = seq;
        uint16_t crc16 = Crc16(timeSyncSignal, 11);
        memcpy(&timeSyncSignal[11], &crc16, sizeof(crc16));

        WriteData((const char*)timeSyncSignal, sizeof(timeSyncSignal));
        seq++;
    };

    timesyncClock rtc_clock;
    struct timespec rtc_ts;
    char pps_path[MAX_PPS_PATH_IMU] = {};
    timesyncPPS hb_pps;
    pps_info_t infobuf;
    int ret = 0;

    ret = find_pps_path(pps_path, "pps");
    if (ret < 0) {
        MLOG_E("find pps path failed");
        exit(0);
    }
    MLOG_I("Find pps path: {} succeed", pps_path);

    ret = timesyncOpenPPS(pps_path, &hb_pps);
    if (ret < 0) {
        MLOG_E("Open pps {} failed", pps_path);
        exit(0);
    }
    MLOG_I("Open pps:{} succeed", pps_path);

    ret = timesyncOpenClock(CLOCK_EXRTC_32768_ID, &rtc_clock);
    if (ret < 0) {
        MLOG_E("Open rtc clock failed");
    }
    MLOG_I("Open rtc clock succeed");

    while (mIsRunning) {
        // 长期无数据，调用复位接口
        auto nowSec = system::GetUptimeSec();
        auto diff = nowSec - mTtyDevice->GetLastRcvSec();
        if (diff >= IMU_RESET_TIMEOUT_SEC) {
            MLOG_E("IMU recv timeout, resetting...");
            Reset();
        }

        // 时间同步
        ret = timesyncFetchPPS(&hb_pps, &infobuf);

        memset(&rtc_ts, 0, sizeof(rtc_ts));
        ret = timesyncGetClockSnapshotTime(&rtc_clock, &rtc_ts);
        
        sendRtcTimeToImu(rtc_ts.tv_sec);
    }
    timesyncClosePPS(&hb_pps);
    timesyncCloseClock(&rtc_clock);
    return;
}

void ImuDevice::Terminate() {
    if (instance != nullptr) {
        dataStatics.reset();
        delete instance;
        instance = nullptr;
    }
}

}  // namespace imu
}  // namespace minieye
