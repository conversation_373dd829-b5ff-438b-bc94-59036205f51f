/*
 * Copyright [2023] MINIEYE
 * Descripttion : IMU接口使用示例
 * Author       : xujiesen
 * Date         : 2025-04-02
 */

#include "ImuDevice.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <atomic>
#include <cstring>
#include <iomanip>

std::atomic<bool> gRunning(true);

void signalHandler(int signum) {
    std::cout << "Interrupt signal (" << signum << ") received." << std::endl;
    gRunning = false;
}

// IMU数据回调函数
void imuDataCallback(const char* data, int32_t dataLen) {
    if (dataLen == 64) {
        uint64_t timestamp = *(uint64_t*)(data + 42);
        std::cout << "Received IMU data, timestamp: " << std::dec << timestamp << ", length: " << dataLen;
    } else {
        std::cout << "Received IMU data, length: " << std::dec << dataLen;
    }
    for (int i = 0; i < dataLen; i++) {
        if (i % 16 == 0) {
            std::cout << std::endl;
        }
        std::cout << std::hex << std::setw(2) << std::setfill('0') << (int)data[i] << " ";
    }
    std::cout << std::endl;
    // 实际应用中，这里应该解析IMU数据，并进行相应处理
    // 例如：解析加速度、角速度、角度等信息
}

int main(int argc, char* argv[]) {
    signal(SIGINT, signalHandler);

    // 获取IMU管理器实例
    minieye::imu::ImuDevice& imu = minieye::imu::ImuDevice::GetInstance();

    // 初始化IMU模块，传入回调函数
    if (!imu.Init(imuDataCallback)) {
        std::cerr << "Failed to initialize IMU module" << std::endl;
        return -1;
    }

    std::cout << "IMU module is running. Press Ctrl+C to exit." << std::endl;

    // 示例：发送配置命令到IMU设备
    const char* configCmd = "CONFIG_IMU_SAMPLE_RATE 100\r\n";
    imu.WriteData(configCmd, static_cast<int32_t>(strlen(configCmd)));

    // 主循环
    int loopCount = 0;
    while (gRunning) {
        std::this_thread::sleep_for(std::chrono::seconds(5));

        // 每隔30秒发送一次状态查询命令
        if (++loopCount % 6 == 0) {
            const char* statusCmd = "GET_IMU_STATUS\r\n";
            imu.WriteData(statusCmd, static_cast<int32_t>(strlen(statusCmd)));
        }

        // 模拟长时间无数据情况下的复位操作
        if (loopCount > 60) {  // 5分钟后模拟复位操作
            std::cout << "No data received for a long time, resetting IMU module..." << std::endl;
            imu.Reset();
            loopCount = 0;  // 重置计数器

            // 重新发送配置命令
            std::this_thread::sleep_for(std::chrono::seconds(2));  // 等待复位完成
            imu.WriteData(configCmd, static_cast<int32_t>(strlen(configCmd)));
        }
    }

    imu.Terminate();
    std::cout << "IMU sample application exited." << std::endl;
    return 0;
}
