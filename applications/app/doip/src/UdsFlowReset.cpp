/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : zhouyuxuan
 * Date         : 2025-04-29
 */

#include "interface/UdsFlowInterface.h"
#include "Launcher.h"

using namespace minieye::dolink;

minieye::uds::FlowResetPreCondChkHdlerHdlerRet_t interface::Reset::HardResetPreCondChk() {
    return VEHICLE_STATUS_SERVICE()->ConditionChk() ? minieye::uds::E_FLOW_RESET_PRE_COND_CHK_HDLER_RET_SUCCESS
                                                    : minieye::uds::E_FLOW_RESET_PRE_COND_CHK_HDLER_RET_COND_ERROR;
}

bool interface::Reset::HardResetHdler() {
    std::system("sync");

    COMPONENT_ISOTP()->ResetRequest();
    return true;
}
