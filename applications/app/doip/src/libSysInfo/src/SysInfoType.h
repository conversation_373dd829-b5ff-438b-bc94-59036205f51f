/*
 * Copyright [2024] MINIEYE
 * Descripttion : 文件描述
 * Author       : yicheng
 * Date         : 2025-06-20
 */
#pragma once
#include <vector>
#include <cstdint>
#include <cstring>
namespace minieye {
namespace dolink {
enum SysInfoType {
    E_SYS_INFO_TYPE_UNKNOW = 0,
    E_SYS_INFO_TYPE_DID = 1,
    E_SYS_INFO_TYPE_ADACU = 2,
    E_SYS_INFO_TYPE_VID,
    E_SYS_INFO_TYPE_SN,
    E_SYS_INFO_TYPE_VIN,
    E_SYS_INFO_TYPE_ENV
};
struct SysInfoMsg {
    int32_t type;
    int32_t length;
    std::vector<char> data;

    int32_t Encode(uint8_t* buf, int len) {
        int32_t index = 0;
        memcpy(buf + index, &type, sizeof(type));
        index += sizeof(type);
        memcpy(buf + index, &length, sizeof(length));
        index += sizeof(length);
        memcpy(buf + index, data.data(), data.size());
        index += data.size();
        return index;
    }
    int32_t Decode(const uint8_t* buf, int len) {
        int32_t index = 0;
        memcpy(&type, buf, sizeof(type));
        index += sizeof(type);
        memcpy(&length, buf + index, sizeof(length));
        index += sizeof(length);
        data.resize(length);
        memcpy(data.data(), buf + index, length);
        index += length;
        if (len != index) {
            return -1;
        }
        return index;
    }
    int32_t Size() {
        int32_t size_ = sizeof(type) + sizeof(length) + data.size();
        return size_;
    }
};
}  // namespace dolink

}  // namespace minieye
