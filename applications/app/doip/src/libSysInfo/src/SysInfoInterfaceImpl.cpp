/*
 * Copyright [2024] MINIEYE
 * Descripttion : 文件描述
 * Author       : yicheng
 * Date         : 2025-06-20
 */
#include <iostream>
#include <memory>
#include <vector>
#include <utility>
#include <string>
#include <cstring>
#include <cstdio>
#include "SysInfoInterfaceImpl.h"

namespace minieye {
namespace dolink {

std::shared_ptr<ISysInfoInterface> ISysInfoInterface::GetInstance() {
    static std::shared_ptr<SysInfoInterfaceImpl> instance = std::make_shared<SysInfoInterfaceImpl>();
    return instance;
}

int SysInfoInterfaceImpl::Init() {
    std::string requestUrl = std::string("ipc:///tmp/minieye_sysinfo_reqres_nn");
    mNnRequestPtr = minieye::nnmsg::NnReqRep::CreateRequester(requestUrl, 2000);
    if (mNnRequestPtr->Init() != 0) {
        std::cout << "Init failed" << std::endl;
        return -1;
    }
    return 0;
}

int SysInfoInterfaceImpl::GetSN(std::vector<char>& value) {
    std::vector<char> data;
    int ret = 0;

    if ((ret = Request(E_SYS_INFO_TYPE_SN, data)) != 0) {
        return ret;
    }

    value = std::move(data);
    return ret;
}

int SysInfoInterfaceImpl::GetVIN(std::vector<char>& value) {
    std::vector<char> data;
    int ret = 0;

    if ((ret = Request(E_SYS_INFO_TYPE_VIN, data)) != 0) {
        return ret;
    }

    value = std::move(data);
    return ret;
}

int SysInfoInterfaceImpl::GetVID(std::string& value) {
    std::vector<char> data;
    int ret = 0;

    if ((ret = Request(E_SYS_INFO_TYPE_VID, data)) != 0) {
        return ret;
    }

    value.assign(data.begin(), data.end());
    return ret;
}

int SysInfoInterfaceImpl::GetEnvInfo(uint8_t& type) {
    std::vector<char> data;
    int ret = 0;

    if ((ret = Request(E_SYS_INFO_TYPE_ENV, data)) != 0) {
        return ret;
    }

    type = data[0];
    return ret;
}

int SysInfoInterfaceImpl::GetCommunicationToken(std::vector<char>& value, uint64_t& valid_utc) {
    std::vector<char> data;
    int ret = 0;

    if ((ret = Request(E_SYS_INFO_TYPE_ADACU, data)) != 0) {
        return ret;
    }
    
    value.assign(data.begin(), data.begin() + 16);
    memcpy(&valid_utc, data.data() + 16, sizeof(uint64_t));
    return 0;
}

int SysInfoInterfaceImpl::Request(SysInfoType type, std::vector<char>& data) {
    SysInfoMsg request;
    request.type   = type;
    request.length = 0;
    request.data.resize(request.length);

    std::vector<uint8_t> buf;
    buf.resize(request.Size());
    request.Encode(buf.data(), request.Size());

    SysInfoMsg reponse;
    reponse.type   = 0;
    reponse.length = 0;

    bool isResponse = false;

    int res = mNnRequestPtr->Request(buf.data(), buf.size(), [this, &reponse, &isResponse](const void* data, int len) {
        // const uint8_t* reponseData = reinterpret_cast<const uint8_t*>(data);
        std::unique_lock<std::mutex> lock(mNnMutex);
        isResponse = true;
        reponse.Decode(reinterpret_cast<const uint8_t*>(data), len);
        mNnConditionCv.notify_all();
    });
    if (res != 0) {
        return -1;
    }

    std::unique_lock<std::mutex> lock(mNnMutex);
    if (!mNnConditionCv.wait_for(lock, std::chrono::milliseconds(2000), [&]() { return isResponse; })) {
        return -1;
    }

    data = std::move(reponse.data);
    return 0;
}

}  // namespace dolink
}  // namespace minieye
