/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : zhouyuxuan
 * Date         : 2025-07-31
 */
#pragma once

#include <cstdint>
#include <memory>
#include <vector>
#include <mutex>
#include <string>
#include <condition_variable>
#include "NnReqRep.h"
#include "ISysInfoInterface.h"
#include "SysInfoType.h"

namespace minieye {
namespace dolink {

class SysInfoInterfaceImpl : public ISysInfoInterface {
 public:
    virtual int Init();
    virtual int GetSN(std::vector<char>& value);
    virtual int GetVIN(std::vector<char>& value);
    virtual int GetVID(std::string& value);
    virtual int GetEnvInfo(uint8_t& type);
    virtual int GetCommunicationToken(std::vector<char>& value, uint64_t& valid_utc);
 private:
    int Request(SysInfoType type, std::vector<char>& data);
 private:
    std::shared_ptr<minieye::nnmsg::NnReqRep> mNnRequestPtr;
    std::condition_variable mNnConditionCv;
    std::mutex mNnMutex;
};

}  // namespace dolink
}  // namespace minieye
