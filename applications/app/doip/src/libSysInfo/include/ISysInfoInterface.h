/*
 * Copyright [2024] MINIEYE
 * Descripttion : 文件描述
 * Author       : yicheng
 * Date         : 2025-06-20
 */
#pragma once

#include <cstdint>
#include <memory>
#include <vector>
#include <string>

namespace minieye {
namespace dolink {

class ISysInfoInterface {
 public:
    /**
     * description: 获取接口实例
     * return {std::shared_ptr<ISysInfoInterface>}：返回接口指针
     */
    static std::shared_ptr<ISysInfoInterface> GetInstance();

    /**
     * description: 接口实例初始化
     * return {int} 0:成功 -1：失败
     */
    virtual int Init() = 0;
    /**
     * description: 获取SN
     * param： {std::vector<char>&} value: SN值
     * return {int} 0：成功， -1：失败
     */
    virtual int GetSN(std::vector<char>& value) = 0;
    /**
     * description: 获取VIN
     * param： {std::vector<char>&} value: VIN值
     * return {int} 0：成功， -1：失败
     */
    virtual int GetVIN(std::vector<char>& value) = 0;
        /**
     * description: 获取 VID
     * param: {std::string&} value: 获取的vid
     * return {int} 0：成功， -1：失败
     */
    virtual int GetVID(std::string& value) = 0;
    /**
     * description: 获取环境信息
     * param： {uint8_t&}     type: 环境类型 (0:未知 1: P环境 2: PP环境 3: QA环境)
     * return {int} 0：成功， -1：失败
     */
    virtual int GetEnvInfo(uint8_t& type) = 0;
    /**
     * description: 获取通信密钥
     * param: {std::vector<char>&} value: 获取的密钥
     *        {uint64_t&} valid_utc: 密钥有效期
     * return {int} 0：成功， -1：失败
     */
    virtual int GetCommunicationToken(std::vector<char>& value, uint64_t& valid_utc) = 0;
};

}  // namespace dolink
}  // namespace minieye
