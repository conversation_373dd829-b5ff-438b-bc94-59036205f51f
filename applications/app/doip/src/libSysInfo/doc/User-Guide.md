
# libminieye_sysinfo User Guide

## 概述

------

### 背景
    SOC中UDS诊断服务由doip模块实现，当前doip模块只会和诊断仪进行交互，无法对其他模块提供相关诊断信息。为实现其他模块可以读取
一些UDS诊断相关数据，增加系统信息获取接口，支持其他模块通过接口获取诸如DID值等相关信息。


## 系统框图
    libminieye_sysinfo 分为两部分：
        1、集成在DoIP模块的服务端 ：接收其他进程模块的请求指令，返回相应的结果。
        2、libminieye_sysinfo_interface： 接口以 libminieye_sysinfo.so 库及头文件形式提供，接口说明见 API-Manual.md
    系统框图如下：
![系统框图](ISysInfoInterface.png)

## 模块介绍

### DoIP模块的服务端
        DoIP服务端实现接收其他模块的DID信息请求，根据请求的did，返回对应的did信息。
    
### libminieye_sysinfo Interface

#### 集成部署
    当应用模块需要接收DID信息时，需要集成libminieye_sysinfo_interface.so接口。

    1、集成libminieye_sysinfo_interface.so，需要包含1个头文件：
            include
            └── ISysInfoInterface.h

#### 接口使用流程
```c++
    // 添加包含头文件：
    #include "ISysInfoInterface.h"

    // 创建指针
    auto mSysInfoInterfacePtr = minieye::status::ISysInfoInterface::GetInstance();
    // 初始化
    mSysInfoInterfacePtr->Init();
    // 获取SN
    std::vector<char> sn;
    mSysInfoInterfacePtr->GetSN(sn);
    // 获取VIN
    std::vector<char> vin;
    mSysInfoInterfacePtr->GetVIN(vin);
    // 获取VID
    std::string vid;
    mSysInfoInterfacePtr->GetVID(vid);
    // 获取环境参数
    uint8_t type;
    mSysInfoInterfacePtr->GetEnvInfo(type);
    // 获取Adacu Key & valid UTC
    std::vector<char> key;
    uint64_t valid_utc;
    mSysInfoInterfacePtr->GetCommunicationToken(key, valid_utc);

```