/*
 * Copyright [2024] MINIEYE
 * Descripttion : 文件描述
 * Author       : yicheng
 * Date         : 2025-06-20
 */
#include <string>
#include <vector>
#include <iostream>
#include <cstdlib>
#include <cstring>
#include <memory>
#include <cstdio>
#include "ISysInfoInterface.h"

#define ARRAY_PRINT(KEY, VALUE)           \
    do {                                  \
        printf("recv " #KEY " value:\n"); \
        for (auto& i : value) {           \
            printf("%x ", i);             \
        }                                 \
        printf("\n");                     \
    } while (0)

class SysInfoTest {
 public:
    int Init() {
        mInterface = minieye::dolink::ISysInfoInterface::GetInstance();
        if (mInterface->Init() != 0) {
            std::cout << "init failed" << std::endl;
            return -1;
        }
        return 0;
    }
    int GetSN() {
        std::vector<char> value;
        mInterface->GetSN(value);
        ARRAY_PRINT(SN, value);
        return 0;
    }
    int GetVIN() {
        std::vector<char> value;
        mInterface->GetVIN(value);
        ARRAY_PRINT(VIN, value);
        return 0;
    }
    int GetVID() {
        std::string value;
        mInterface->GetVID(value);
        printf("recv vid: %s\n", value.c_str());
        return 0;
    }
    int GetEnv() {
        uint8_t type;
        mInterface->GetEnvInfo(type);
        printf("cur env: %02x\n", type);
        return 0;
    }
    int GetCommunicationToken() {
        std::vector<char> value;
        uint64_t valid_utc;
        mInterface->GetCommunicationToken(value, valid_utc);
        ARRAY_PRINT(Key, value);
        printf("valid UTC: %lu\n", valid_utc);
        return 0;
    }

 private:
    std::shared_ptr<minieye::dolink::ISysInfoInterface> mInterface;
};

int main(int argc, char** argv) {
    if (argc < 2) {
        std::cout << "Invalid parameter!" << std::endl;
        return -1;
    }

    SysInfoTest test;
    if (test.Init() != 0) {
        return -1;
    }

    switch (argv[1][0]) {
        case 'a':
            test.GetCommunicationToken();
            return 0;
        case 's':
            test.GetSN();
            return 0;
        case 'v':
            test.GetVIN();
            return 0;
        case 'i':
            test.GetVID();
            return 0;
        case 'e':
            test.GetEnv();
            return 0;
    }

    return 0;
}