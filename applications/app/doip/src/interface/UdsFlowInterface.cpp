
/*
 * DOIP CODE GENERATOR BY MINIEYE, DO NOT MODIFY THIS FILE!
 * 
 *                 @ MINIEYE CopyRight  
 *
 * Generated Date: 2025/8/22 15:41:15
 * See More infos: (TODO)
 */

#include "UdsFlowInterface.h"

// parasoft-instrumentation off

using namespace minieye::dolink::interface;


/////////////////////////////// FaultStorage ///////////////////////////////

static std::map<uint32_t, Fault::DtcMapInfo_t> gDtcMap = {
    {
        0xE01088,
        {
            decode_can_0x10000727_DTC_0xE01088_TestFailed,
            0x0001,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE01288,
        {
            decode_can_0x10000727_DTC_0xE01288_TestFailed,
            0x0002,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE01388,
        {
            decode_can_0x10000727_DTC_0xE01388_TestFailed,
            0x0003,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE00788,
        {
            decode_can_0x10000727_DTC_0xE00788_TestFailed,
            0x0004,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AB083,
        {
            decode_can_0x10000727_DTC_0x4AB083_TestFailed,
            0x0005,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AB383,
        {
            decode_can_0x10000727_DTC_0x4AB383_TestFailed,
            0x0006,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AB483,
        {
            decode_can_0x10000727_DTC_0x4AB483_TestFailed,
            0x0007,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AB583,
        {
            decode_can_0x10000727_DTC_0x4AB583_TestFailed,
            0x0008,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AB683,
        {
            decode_can_0x10000727_DTC_0x4AB683_TestFailed,
            0x0009,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AB783,
        {
            decode_can_0x10000727_DTC_0x4AB783_TestFailed,
            0x000A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4ABA83,
        {
            decode_can_0x10000727_DTC_0x4ABA83_TestFailed,
            0x000B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4ABB83,
        {
            decode_can_0x10000727_DTC_0x4ABB83_TestFailed,
            0x000C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4ABD83,
        {
            decode_can_0x10000727_DTC_0x4ABD83_TestFailed,
            0x000D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4ACE83,
        {
            decode_can_0x10000727_DTC_0x4ACE83_TestFailed,
            0x000E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13587,
        {
            decode_can_0x10000727_DTC_0xC13587_TestFailed,
            0x000F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13787,
        {
            decode_can_0x10000727_DTC_0xC13787_TestFailed,
            0x0010,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13D87,
        {
            decode_can_0x10000727_DTC_0xC13D87_TestFailed,
            0x0011,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13E87,
        {
            decode_can_0x10000727_DTC_0xC13E87_TestFailed,
            0x0012,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13F87,
        {
            decode_can_0x10000727_DTC_0xC13F87_TestFailed,
            0x0013,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC25987,
        {
            decode_can_0x10000727_DTC_0xC25987_TestFailed,
            0x0014,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13887,
        {
            decode_can_0x10000727_DTC_0xC13887_TestFailed,
            0x0015,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13187,
        {
            decode_can_0x10000727_DTC_0xC13187_TestFailed,
            0x0016,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13287,
        {
            decode_can_0x10000727_DTC_0xC13287_TestFailed,
            0x0017,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC14187,
        {
            decode_can_0x10000727_DTC_0xC14187_TestFailed,
            0x0018,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC25A87,
        {
            decode_can_0x10000727_DTC_0xC25A87_TestFailed,
            0x0019,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC25887,
        {
            decode_can_0x10000727_DTC_0xC25887_TestFailed,
            0x001A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13487,
        {
            decode_can_0x10000727_DTC_0xC13487_TestFailed,
            0x001B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13687,
        {
            decode_can_0x10000727_DTC_0xC13687_TestFailed,
            0x001C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xC13987,
        {
            decode_can_0x10000727_DTC_0xC13987_TestFailed,
            0x001D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF008,
        {
            decode_can_0x10000727_DTC_0x4AF008_TestFailed,
            0x001E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF108,
        {
            decode_can_0x10000727_DTC_0x4AF108_TestFailed,
            0x001F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF208,
        {
            decode_can_0x10000727_DTC_0x4AF208_TestFailed,
            0x0020,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF308,
        {
            decode_can_0x10000727_DTC_0x4AF308_TestFailed,
            0x0021,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF508,
        {
            decode_can_0x10000727_DTC_0x4AF508_TestFailed,
            0x0022,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF608,
        {
            decode_can_0x10000727_DTC_0x4AF608_TestFailed,
            0x0023,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF708,
        {
            decode_can_0x10000727_DTC_0x4AF708_TestFailed,
            0x0024,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF808,
        {
            decode_can_0x10000727_DTC_0x4AF808_TestFailed,
            0x0025,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AF908,
        {
            decode_can_0x10000727_DTC_0x4AF908_TestFailed,
            0x0026,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AFA08,
        {
            decode_can_0x10000727_DTC_0x4AFA08_TestFailed,
            0x0027,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AFB08,
        {
            decode_can_0x10000727_DTC_0x4AFB08_TestFailed,
            0x0028,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AFC08,
        {
            decode_can_0x10000727_DTC_0x4AFC08_TestFailed,
            0x0029,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AFD08,
        {
            decode_can_0x10000727_DTC_0x4AFD08_TestFailed,
            0x002A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AFE08,
        {
            decode_can_0x10000727_DTC_0x4AFE08_TestFailed,
            0x002B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0B08,
        {
            decode_can_0x10000727_DTC_0x4B0B08_TestFailed,
            0x002C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0C08,
        {
            decode_can_0x10000727_DTC_0x4B0C08_TestFailed,
            0x002D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0D08,
        {
            decode_can_0x10000727_DTC_0x4B0D08_TestFailed,
            0x002E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4AFF08,
        {
            decode_can_0x10000727_DTC_0x4AFF08_TestFailed,
            0x002F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0008,
        {
            decode_can_0x10000727_DTC_0x4B0008_TestFailed,
            0x0030,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0108,
        {
            decode_can_0x10000727_DTC_0x4B0108_TestFailed,
            0x0031,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0208,
        {
            decode_can_0x10000727_DTC_0x4B0208_TestFailed,
            0x0032,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0608,
        {
            decode_can_0x10000727_DTC_0x4B0608_TestFailed,
            0x0033,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0708,
        {
            decode_can_0x10000727_DTC_0x4B0708_TestFailed,
            0x0034,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0308,
        {
            decode_can_0x10000727_DTC_0x4B0308_TestFailed,
            0x0035,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0808,
        {
            decode_can_0x10000727_DTC_0x4B0808_TestFailed,
            0x0036,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0908,
        {
            decode_can_0x10000727_DTC_0x4B0908_TestFailed,
            0x0037,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0A08,
        {
            decode_can_0x10000727_DTC_0x4B0A08_TestFailed,
            0x0038,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4504,
        {
            decode_can_0x10000727_DTC_0x4B4504_TestFailed,
            0x0039,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4804,
        {
            decode_can_0x10000727_DTC_0x4B4804_TestFailed,
            0x003A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4904,
        {
            decode_can_0x10000727_DTC_0x4B4904_TestFailed,
            0x003B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1697,
        {
            decode_can_0x10000727_DTC_0x4B1697_TestFailed,
            0x003C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1797,
        {
            decode_can_0x10000727_DTC_0x4B1797_TestFailed,
            0x003D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1897,
        {
            decode_can_0x10000727_DTC_0x4B1897_TestFailed,
            0x003E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1997,
        {
            decode_can_0x10000727_DTC_0x4B1997_TestFailed,
            0x003F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1A97,
        {
            decode_can_0x10000727_DTC_0x4B1A97_TestFailed,
            0x0040,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1B97,
        {
            decode_can_0x10000727_DTC_0x4B1B97_TestFailed,
            0x0041,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1C97,
        {
            decode_can_0x10000727_DTC_0x4B1C97_TestFailed,
            0x0042,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1D97,
        {
            decode_can_0x10000727_DTC_0x4B1D97_TestFailed,
            0x0043,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1E97,
        {
            decode_can_0x10000727_DTC_0x4B1E97_TestFailed,
            0x0044,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B1F97,
        {
            decode_can_0x10000727_DTC_0x4B1F97_TestFailed,
            0x0045,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B2097,
        {
            decode_can_0x10000727_DTC_0x4B2097_TestFailed,
            0x0046,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5097,
        {
            decode_can_0x10000727_DTC_0x4B5097_TestFailed,
            0x0047,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5397,
        {
            decode_can_0x10000727_DTC_0x4B5397_TestFailed,
            0x0048,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5497,
        {
            decode_can_0x10000727_DTC_0x4B5497_TestFailed,
            0x0049,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4A05,
        {
            decode_can_0x10000727_DTC_0x4B4A05_TestFailed,
            0x004A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4D05,
        {
            decode_can_0x10000727_DTC_0x4B4D05_TestFailed,
            0x004B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4E05,
        {
            decode_can_0x10000727_DTC_0x4B4E05_TestFailed,
            0x004C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6657,
        {
            decode_can_0x10000727_DTC_0x4B6657_TestFailed,
            0x004D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6757,
        {
            decode_can_0x10000727_DTC_0x4B6757_TestFailed,
            0x004E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6857,
        {
            decode_can_0x10000727_DTC_0x4B6857_TestFailed,
            0x004F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6957,
        {
            decode_can_0x10000727_DTC_0x4B6957_TestFailed,
            0x0050,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6A57,
        {
            decode_can_0x10000727_DTC_0x4B6A57_TestFailed,
            0x0051,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6B57,
        {
            decode_can_0x10000727_DTC_0x4B6B57_TestFailed,
            0x0052,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B2157,
        {
            decode_can_0x10000727_DTC_0x4B2157_TestFailed,
            0x0053,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B2257,
        {
            decode_can_0x10000727_DTC_0x4B2257_TestFailed,
            0x0054,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6C57,
        {
            decode_can_0x10000727_DTC_0x4B6C57_TestFailed,
            0x0055,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6D57,
        {
            decode_can_0x10000727_DTC_0x4B6D57_TestFailed,
            0x0056,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6E57,
        {
            decode_can_0x10000727_DTC_0x4B6E57_TestFailed,
            0x0057,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4087,
        {
            decode_can_0x10000727_DTC_0x4B4087_TestFailed,
            0x0058,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4387,
        {
            decode_can_0x10000727_DTC_0x4B4387_TestFailed,
            0x0059,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4487,
        {
            decode_can_0x10000727_DTC_0x4B4487_TestFailed,
            0x005A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5505,
        {
            decode_can_0x10000727_DTC_0x4B5505_TestFailed,
            0x005B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5605,
        {
            decode_can_0x10000727_DTC_0x4B5605_TestFailed,
            0x005C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5705,
        {
            decode_can_0x10000727_DTC_0x4B5705_TestFailed,
            0x005D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5805,
        {
            decode_can_0x10000727_DTC_0x4B5805_TestFailed,
            0x005E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5905,
        {
            decode_can_0x10000727_DTC_0x4B5905_TestFailed,
            0x005F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5A05,
        {
            decode_can_0x10000727_DTC_0x4B5A05_TestFailed,
            0x0060,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5B04,
        {
            decode_can_0x10000727_DTC_0x4B5B04_TestFailed,
            0x0061,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5C04,
        {
            decode_can_0x10000727_DTC_0x4B5C04_TestFailed,
            0x0062,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5D04,
        {
            decode_can_0x10000727_DTC_0x4B5D04_TestFailed,
            0x0063,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5E05,
        {
            decode_can_0x10000727_DTC_0x4B5E05_TestFailed,
            0x0064,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B5F05,
        {
            decode_can_0x10000727_DTC_0x4B5F05_TestFailed,
            0x0065,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6005,
        {
            decode_can_0x10000727_DTC_0x4B6005_TestFailed,
            0x0066,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6367,
        {
            decode_can_0x10000727_DTC_0x4B6367_TestFailed,
            0x0067,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B6F04,
        {
            decode_can_0x10000727_DTC_0x4B6F04_TestFailed,
            0x0068,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B7105,
        {
            decode_can_0x10000727_DTC_0x4B7105_TestFailed,
            0x0069,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B7205,
        {
            decode_can_0x10000727_DTC_0x4B7205_TestFailed,
            0x006A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B9D89,
        {
            decode_can_0x10000727_DTC_0x4B9D89_TestFailed,
            0x006B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601011,
        {
            decode_can_0x10000727_DTC_0x601011_TestFailed,
            0x006C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE22513,
        {
            decode_can_0x10000727_DTC_0xE22513_TestFailed,
            0x006D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE99009,
        {
            decode_can_0x10000727_DTC_0xE99009_TestFailed,
            0x006E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B4C05,
        {
            decode_can_0x10000727_DTC_0x4B4C05_TestFailed,
            0x006F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4B0508,
        {
            decode_can_0x10000727_DTC_0x4B0508_TestFailed,
            0x0070,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60024B,
        {
            decode_can_0x10000727_DTC_0x60024B_TestFailed,
            0x0071,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60034B,
        {
            decode_can_0x10000727_DTC_0x60034B_TestFailed,
            0x0072,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xD30055,
        {
            decode_can_0x10000727_DTC_0xD30055_TestFailed,
            0x0073,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xF00316,
        {
            decode_can_0x10000727_DTC_0xF00316_TestFailed,
            0x0074,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xF00317,
        {
            decode_can_0x10000727_DTC_0xF00317_TestFailed,
            0x0075,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60041C,
        {
            decode_can_0x10000727_DTC_0x60041C_TestFailed,
            0x0076,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60051C,
        {
            decode_can_0x10000727_DTC_0x60051C_TestFailed,
            0x0077,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60061C,
        {
            decode_can_0x10000727_DTC_0x60061C_TestFailed,
            0x0078,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60071C,
        {
            decode_can_0x10000727_DTC_0x60071C_TestFailed,
            0x0079,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601214,
        {
            decode_can_0x10000727_DTC_0x601214_TestFailed,
            0x007A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2018A,
        {
            decode_can_0x10000727_DTC_0xE2018A_TestFailed,
            0x007B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2028A,
        {
            decode_can_0x10000727_DTC_0xE2028A_TestFailed,
            0x007C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604600,
        {
            decode_can_0x10000727_DTC_0x604600_TestFailed,
            0x007D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605309,
        {
            decode_can_0x10000727_DTC_0x605309_TestFailed,
            0x007E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x603A00,
        {
            decode_can_0x10000727_DTC_0x603A00_TestFailed,
            0x007F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601314,
        {
            decode_can_0x10000727_DTC_0x601314_TestFailed,
            0x0080,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2038A,
        {
            decode_can_0x10000727_DTC_0xE2038A_TestFailed,
            0x0081,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2048A,
        {
            decode_can_0x10000727_DTC_0xE2048A_TestFailed,
            0x0082,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604800,
        {
            decode_can_0x10000727_DTC_0x604800_TestFailed,
            0x0083,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605409,
        {
            decode_can_0x10000727_DTC_0x605409_TestFailed,
            0x0084,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x603B00,
        {
            decode_can_0x10000727_DTC_0x603B00_TestFailed,
            0x0085,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601414,
        {
            decode_can_0x10000727_DTC_0x601414_TestFailed,
            0x0086,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2058A,
        {
            decode_can_0x10000727_DTC_0xE2058A_TestFailed,
            0x0087,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2068A,
        {
            decode_can_0x10000727_DTC_0xE2068A_TestFailed,
            0x0088,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604A00,
        {
            decode_can_0x10000727_DTC_0x604A00_TestFailed,
            0x0089,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605509,
        {
            decode_can_0x10000727_DTC_0x605509_TestFailed,
            0x008A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x603C00,
        {
            decode_can_0x10000727_DTC_0x603C00_TestFailed,
            0x008B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601514,
        {
            decode_can_0x10000727_DTC_0x601514_TestFailed,
            0x008C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2078A,
        {
            decode_can_0x10000727_DTC_0xE2078A_TestFailed,
            0x008D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2088A,
        {
            decode_can_0x10000727_DTC_0xE2088A_TestFailed,
            0x008E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604C00,
        {
            decode_can_0x10000727_DTC_0x604C00_TestFailed,
            0x008F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605609,
        {
            decode_can_0x10000727_DTC_0x605609_TestFailed,
            0x0090,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x603D00,
        {
            decode_can_0x10000727_DTC_0x603D00_TestFailed,
            0x0091,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601614,
        {
            decode_can_0x10000727_DTC_0x601614_TestFailed,
            0x0092,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2098A,
        {
            decode_can_0x10000727_DTC_0xE2098A_TestFailed,
            0x0093,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2108A,
        {
            decode_can_0x10000727_DTC_0xE2108A_TestFailed,
            0x0094,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604E00,
        {
            decode_can_0x10000727_DTC_0x604E00_TestFailed,
            0x0095,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605709,
        {
            decode_can_0x10000727_DTC_0x605709_TestFailed,
            0x0096,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x603E00,
        {
            decode_can_0x10000727_DTC_0x603E00_TestFailed,
            0x0097,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601714,
        {
            decode_can_0x10000727_DTC_0x601714_TestFailed,
            0x0098,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2118A,
        {
            decode_can_0x10000727_DTC_0xE2118A_TestFailed,
            0x0099,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2128A,
        {
            decode_can_0x10000727_DTC_0xE2128A_TestFailed,
            0x009A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605000,
        {
            decode_can_0x10000727_DTC_0x605000_TestFailed,
            0x009B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605809,
        {
            decode_can_0x10000727_DTC_0x605809_TestFailed,
            0x009C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x603F00,
        {
            decode_can_0x10000727_DTC_0x603F00_TestFailed,
            0x009D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601814,
        {
            decode_can_0x10000727_DTC_0x601814_TestFailed,
            0x009E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2138A,
        {
            decode_can_0x10000727_DTC_0xE2138A_TestFailed,
            0x009F,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2148A,
        {
            decode_can_0x10000727_DTC_0xE2148A_TestFailed,
            0x00A0,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605200,
        {
            decode_can_0x10000727_DTC_0x605200_TestFailed,
            0x00A1,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605909,
        {
            decode_can_0x10000727_DTC_0x605909_TestFailed,
            0x00A2,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604000,
        {
            decode_can_0x10000727_DTC_0x604000_TestFailed,
            0x00A3,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601914,
        {
            decode_can_0x10000727_DTC_0x601914_TestFailed,
            0x00A4,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2158A,
        {
            decode_can_0x10000727_DTC_0xE2158A_TestFailed,
            0x00A5,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2168A,
        {
            decode_can_0x10000727_DTC_0xE2168A_TestFailed,
            0x00A6,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605300,
        {
            decode_can_0x10000727_DTC_0x605300_TestFailed,
            0x00A7,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605A09,
        {
            decode_can_0x10000727_DTC_0x605A09_TestFailed,
            0x00A8,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604100,
        {
            decode_can_0x10000727_DTC_0x604100_TestFailed,
            0x00A9,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x602014,
        {
            decode_can_0x10000727_DTC_0x602014_TestFailed,
            0x00AA,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2178A,
        {
            decode_can_0x10000727_DTC_0xE2178A_TestFailed,
            0x00AB,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2188A,
        {
            decode_can_0x10000727_DTC_0xE2188A_TestFailed,
            0x00AC,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605400,
        {
            decode_can_0x10000727_DTC_0x605400_TestFailed,
            0x00AD,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605B09,
        {
            decode_can_0x10000727_DTC_0x605B09_TestFailed,
            0x00AE,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604200,
        {
            decode_can_0x10000727_DTC_0x604200_TestFailed,
            0x00AF,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x602114,
        {
            decode_can_0x10000727_DTC_0x602114_TestFailed,
            0x00B0,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2198A,
        {
            decode_can_0x10000727_DTC_0xE2198A_TestFailed,
            0x00B1,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2208A,
        {
            decode_can_0x10000727_DTC_0xE2208A_TestFailed,
            0x00B2,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605500,
        {
            decode_can_0x10000727_DTC_0x605500_TestFailed,
            0x00B3,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605C09,
        {
            decode_can_0x10000727_DTC_0x605C09_TestFailed,
            0x00B4,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604300,
        {
            decode_can_0x10000727_DTC_0x604300_TestFailed,
            0x00B5,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x602214,
        {
            decode_can_0x10000727_DTC_0x602214_TestFailed,
            0x00B6,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2218A,
        {
            decode_can_0x10000727_DTC_0xE2218A_TestFailed,
            0x00B7,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0xE2228A,
        {
            decode_can_0x10000727_DTC_0xE2228A_TestFailed,
            0x00B8,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605600,
        {
            decode_can_0x10000727_DTC_0x605600_TestFailed,
            0x00B9,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x605D09,
        {
            decode_can_0x10000727_DTC_0x605D09_TestFailed,
            0x00BA,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x604400,
        {
            decode_can_0x10000727_DTC_0x604400_TestFailed,
            0x00BB,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60CC4B,
        {
            decode_can_0x10000727_DTC_0x60CC4B_TestFailed,
            0x00BC,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60CD4B,
        {
            decode_can_0x10000727_DTC_0x60CD4B_TestFailed,
            0x00BD,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608109,
        {
            decode_can_0x10000727_DTC_0x608109_TestFailed,
            0x00BE,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608209,
        {
            decode_can_0x10000727_DTC_0x608209_TestFailed,
            0x00BF,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608309,
        {
            decode_can_0x10000727_DTC_0x608309_TestFailed,
            0x00C0,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608409,
        {
            decode_can_0x10000727_DTC_0x608409_TestFailed,
            0x00C1,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608509,
        {
            decode_can_0x10000727_DTC_0x608509_TestFailed,
            0x00C2,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608609,
        {
            decode_can_0x10000727_DTC_0x608609_TestFailed,
            0x00C3,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608709,
        {
            decode_can_0x10000727_DTC_0x608709_TestFailed,
            0x00C4,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608809,
        {
            decode_can_0x10000727_DTC_0x608809_TestFailed,
            0x00C5,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608909,
        {
            decode_can_0x10000727_DTC_0x608909_TestFailed,
            0x00C6,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608A09,
        {
            decode_can_0x10000727_DTC_0x608A09_TestFailed,
            0x00C7,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608B09,
        {
            decode_can_0x10000727_DTC_0x608B09_TestFailed,
            0x00C8,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608C09,
        {
            decode_can_0x10000727_DTC_0x608C09_TestFailed,
            0x00C9,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608D09,
        {
            decode_can_0x10000727_DTC_0x608D09_TestFailed,
            0x00CA,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608E09,
        {
            decode_can_0x10000727_DTC_0x608E09_TestFailed,
            0x00CB,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x608F09,
        {
            decode_can_0x10000727_DTC_0x608F09_TestFailed,
            0x00CC,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609009,
        {
            decode_can_0x10000727_DTC_0x609009_TestFailed,
            0x00CD,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609109,
        {
            decode_can_0x10000727_DTC_0x609109_TestFailed,
            0x00CE,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609209,
        {
            decode_can_0x10000727_DTC_0x609209_TestFailed,
            0x00CF,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609309,
        {
            decode_can_0x10000727_DTC_0x609309_TestFailed,
            0x00D0,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609409,
        {
            decode_can_0x10000727_DTC_0x609409_TestFailed,
            0x00D1,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609509,
        {
            decode_can_0x10000727_DTC_0x609509_TestFailed,
            0x00D2,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609609,
        {
            decode_can_0x10000727_DTC_0x609609_TestFailed,
            0x00D3,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609709,
        {
            decode_can_0x10000727_DTC_0x609709_TestFailed,
            0x00D4,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609809,
        {
            decode_can_0x10000727_DTC_0x609809_TestFailed,
            0x00D5,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609909,
        {
            decode_can_0x10000727_DTC_0x609909_TestFailed,
            0x00D6,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609A09,
        {
            decode_can_0x10000727_DTC_0x609A09_TestFailed,
            0x00D7,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609B09,
        {
            decode_can_0x10000727_DTC_0x609B09_TestFailed,
            0x00D8,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609C09,
        {
            decode_can_0x10000727_DTC_0x609C09_TestFailed,
            0x00D9,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609D09,
        {
            decode_can_0x10000727_DTC_0x609D09_TestFailed,
            0x00DA,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609E09,
        {
            decode_can_0x10000727_DTC_0x609E09_TestFailed,
            0x00DB,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x609F09,
        {
            decode_can_0x10000727_DTC_0x609F09_TestFailed,
            0x00DC,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A009,
        {
            decode_can_0x10000727_DTC_0x60A009_TestFailed,
            0x00DD,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A109,
        {
            decode_can_0x10000727_DTC_0x60A109_TestFailed,
            0x00DE,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A209,
        {
            decode_can_0x10000727_DTC_0x60A209_TestFailed,
            0x00DF,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A309,
        {
            decode_can_0x10000727_DTC_0x60A309_TestFailed,
            0x00E0,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A409,
        {
            decode_can_0x10000727_DTC_0x60A409_TestFailed,
            0x00E1,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A509,
        {
            decode_can_0x10000727_DTC_0x60A509_TestFailed,
            0x00E2,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A609,
        {
            decode_can_0x10000727_DTC_0x60A609_TestFailed,
            0x00E3,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A709,
        {
            decode_can_0x10000727_DTC_0x60A709_TestFailed,
            0x00E4,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A809,
        {
            decode_can_0x10000727_DTC_0x60A809_TestFailed,
            0x00E5,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60A909,
        {
            decode_can_0x10000727_DTC_0x60A909_TestFailed,
            0x00E6,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60AA09,
        {
            decode_can_0x10000727_DTC_0x60AA09_TestFailed,
            0x00E7,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60AB09,
        {
            decode_can_0x10000727_DTC_0x60AB09_TestFailed,
            0x00E8,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60AC09,
        {
            decode_can_0x10000727_DTC_0x60AC09_TestFailed,
            0x00E9,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60AD09,
        {
            decode_can_0x10000727_DTC_0x60AD09_TestFailed,
            0x00EA,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60AE09,
        {
            decode_can_0x10000727_DTC_0x60AE09_TestFailed,
            0x00EB,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60AF09,
        {
            decode_can_0x10000727_DTC_0x60AF09_TestFailed,
            0x00EC,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B009,
        {
            decode_can_0x10000727_DTC_0x60B009_TestFailed,
            0x00ED,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B109,
        {
            decode_can_0x10000727_DTC_0x60B109_TestFailed,
            0x00EE,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B209,
        {
            decode_can_0x10000727_DTC_0x60B209_TestFailed,
            0x00EF,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B309,
        {
            decode_can_0x10000727_DTC_0x60B309_TestFailed,
            0x00F0,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B409,
        {
            decode_can_0x10000727_DTC_0x60B409_TestFailed,
            0x00F1,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B509,
        {
            decode_can_0x10000727_DTC_0x60B509_TestFailed,
            0x00F2,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B609,
        {
            decode_can_0x10000727_DTC_0x60B609_TestFailed,
            0x00F3,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B709,
        {
            decode_can_0x10000727_DTC_0x60B709_TestFailed,
            0x00F4,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B809,
        {
            decode_can_0x10000727_DTC_0x60B809_TestFailed,
            0x00F5,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60B909,
        {
            decode_can_0x10000727_DTC_0x60B909_TestFailed,
            0x00F6,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60BA09,
        {
            decode_can_0x10000727_DTC_0x60BA09_TestFailed,
            0x00F7,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60BB09,
        {
            decode_can_0x10000727_DTC_0x60BB09_TestFailed,
            0x00F8,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60BC09,
        {
            decode_can_0x10000727_DTC_0x60BC09_TestFailed,
            0x00F9,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60BD09,
        {
            decode_can_0x10000727_DTC_0x60BD09_TestFailed,
            0x00FA,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60BE09,
        {
            decode_can_0x10000727_DTC_0x60BE09_TestFailed,
            0x00FB,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60BF09,
        {
            decode_can_0x10000727_DTC_0x60BF09_TestFailed,
            0x00FC,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C009,
        {
            decode_can_0x10000727_DTC_0x60C009_TestFailed,
            0x00FD,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C109,
        {
            decode_can_0x10000727_DTC_0x60C109_TestFailed,
            0x00FE,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C209,
        {
            decode_can_0x10000727_DTC_0x60C209_TestFailed,
            0x00FF,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C309,
        {
            decode_can_0x10000727_DTC_0x60C309_TestFailed,
            0x0100,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C409,
        {
            decode_can_0x10000727_DTC_0x60C409_TestFailed,
            0x0101,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C509,
        {
            decode_can_0x10000727_DTC_0x60C509_TestFailed,
            0x0102,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C609,
        {
            decode_can_0x10000727_DTC_0x60C609_TestFailed,
            0x0103,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C709,
        {
            decode_can_0x10000727_DTC_0x60C709_TestFailed,
            0x0104,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C809,
        {
            decode_can_0x10000727_DTC_0x60C809_TestFailed,
            0x0105,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60C91C,
        {
            decode_can_0x10000727_DTC_0x60C91C_TestFailed,
            0x0106,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60CA1C,
        {
            decode_can_0x10000727_DTC_0x60CA1C_TestFailed,
            0x0107,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x60CB1C,
        {
            decode_can_0x10000727_DTC_0x60CB1C_TestFailed,
            0x0108,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4ABE83,
        {
            decode_can_0x10000727_DTC_0x4ABE83_TestFailed,
            0x0109,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x4ABF83,
        {
            decode_can_0x10000727_DTC_0x4ABF83_TestFailed,
            0x010A,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601A09,
        {
            decode_can_0x10000727_DTC_0x601A09_TestFailed,
            0x010B,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601B09,
        {
            decode_can_0x10000727_DTC_0x601B09_TestFailed,
            0x010C,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601C09,
        {
            decode_can_0x10000727_DTC_0x601C09_TestFailed,
            0x010D,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
    {
        0x601D09,
        {
            decode_can_0x10000727_DTC_0x601D09_TestFailed,
            0x010E,
            0x00,
            40,
            {
                0x01,
            },
            {
                0x01,
                0x02,
            },
        }
    },
};
std::map<uint32_t, Fault::DtcMapInfo_t> & Fault::GetMap() {
    return gDtcMap;
}

/////////////////////////////// UdsFlowAccess ///////////////////////////////

static minieye::uds::UdsFlowSessionCtrlCfg_t kSessionCtrlCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_DEFAULT,
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdlerList = {
        {
            .sessionType = minieye::uds::E_SESSION_TYPE_DEFAULT,
                .hdler = []() -> bool {
                    return Access::Session_Default_Hdler();
                },
                .condition = {
                    .addrMethodList = {
                        minieye::uds::E_ADDR_METHOD_LOGICAL,
                        minieye::uds::E_ADDR_METHOD_FUNCTION,
                    },
                    .sessionAccessTypeList = {
                        minieye::uds::E_SESSION_TYPE_DEFAULT,
                        minieye::uds::E_SESSION_TYPE_EXTENDED,
                        minieye::uds::E_SESSION_TYPE_OTA,
                    },
                    .securityAccessLevelList = {
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                    },
                },
        },
        {
            .sessionType = minieye::uds::E_SESSION_TYPE_EXTENDED,
            .hdler = []() -> bool {
                return Access::Session_Extended_Hdler();
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .sessionType = minieye::uds::E_SESSION_TYPE_OTA,
            .hdler = []() -> bool {
                return Access::Session_OTA_Hdler();
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
    },
};
static minieye::uds::UdsFlowSendKeyCfg_t kSendKeyCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdlerList = {
        {
            .securityAccessType = 0x02,
                .keyLen = 4,
                .hdler = [](const std::vector<char> & seed,
                    const std::vector<char> & sendKey,
                        minieye::uds::SecurityAccessLevel_t & securityAccessLevel) -> minieye::uds::FlowSendKeyHdlerRet_t {
                    return Access::Security_SendKey_0x02_Hdler(seed, sendKey, securityAccessLevel);
                },
                .condition = {
                    .addrMethodList = {
                        minieye::uds::E_ADDR_METHOD_LOGICAL,
                    },
                    .sessionAccessTypeList = {
                        minieye::uds::E_SESSION_TYPE_EXTENDED,
                    },
                    .securityAccessLevelList = {
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    },
                },
        },
        {
            .securityAccessType = 0x04,
            .keyLen = 4,
            .hdler = [](const std::vector<char> & seed,
                const std::vector<char> & sendKey,
                    minieye::uds::SecurityAccessLevel_t & securityAccessLevel) -> minieye::uds::FlowSendKeyHdlerRet_t {
                return Access::Security_SendKey_0x04_Hdler(seed, sendKey, securityAccessLevel);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
    },
};
static minieye::uds::UdsFlowRequestSeedCfg_t kRequestSeedCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdlerList = {
        {
            .securityAccessType = 0x01,
                .seedLen = 4,
                .hdler = [](std::vector<char> & seed) -> minieye::uds::FlowRequestSeedHdlerRet_t {
                    return Access::Security_RequestSeed_0x01_Hdler(seed);
                },
                .condition = {
                    .addrMethodList = {
                        minieye::uds::E_ADDR_METHOD_LOGICAL,
                    },
                    .sessionAccessTypeList = {
                        minieye::uds::E_SESSION_TYPE_EXTENDED,
                    },
                    .securityAccessLevelList = {
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    },
                },
        },
        {
            .securityAccessType = 0x03,
            .seedLen = 4,
            .hdler = [](std::vector<char> & seed) -> minieye::uds::FlowRequestSeedHdlerRet_t {
                return Access::Security_RequestSeed_0x03_Hdler(seed);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
    },
};

/////////////////////////////// UdsFlowCommCtrl ///////////////////////////////

static minieye::uds::UdsFlowCommCtrlCfg_t kCommCtrlCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_EXTENDED,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
            },
        },
    },
    .hdlerList = {
        {
            .ctrlType = minieye::uds::E_FLOW_COMM_CTRL_TYPE_ENABLE_RX_ENABLE_TX,
                .subFuncList = {
                    {
                        .commType = minieye::uds::E_FLOW_COMM_TYPE_APP_MESSAGE,
                            .commChn = 0x00,
                            .hdler = []() -> minieye::uds::FlowCommCtrlHdlerRet_t {
                                return CommCtrl::Chn_0x00_EnableRxEnableTx_AppMessage_Hdler();
                            },
                            .condition = {
                                .addrMethodList = {
                                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                                },
                                .sessionAccessTypeList = {
                                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                                },
                                .securityAccessLevelList = {
                                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                                },
                            },
                    },
                    {
                        .commType = minieye::uds::E_FLOW_COMM_TYPE_NETWORK_MNGER_MESSAGE,
                        .commChn = 0x00,
                        .hdler = []() -> minieye::uds::FlowCommCtrlHdlerRet_t {
                            return CommCtrl::Chn_0x00_EnableRxEnableTx_NetworkMessge_Hdler();
                        },
                        .condition = {
                            .addrMethodList = {
                                minieye::uds::E_ADDR_METHOD_LOGICAL,
                                minieye::uds::E_ADDR_METHOD_FUNCTION,
                            },
                            .sessionAccessTypeList = {
                                minieye::uds::E_SESSION_TYPE_DEFAULT,
                                minieye::uds::E_SESSION_TYPE_EXTENDED,
                            },
                            .securityAccessLevelList = {
                                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                            },
                        },
                    },
                    {
                        .commType = minieye::uds::E_FLOW_COMM_TYPE_APP_AND_NETWORK_MNGER_MESSAGE,
                        .commChn = 0x00,
                        .hdler = []() -> minieye::uds::FlowCommCtrlHdlerRet_t {
                            return CommCtrl::Chn_0x00_EnableRxEnableTx_AppAndNetworkMessge_Hdler();
                        },
                        .condition = {
                            .addrMethodList = {
                                minieye::uds::E_ADDR_METHOD_LOGICAL,
                                minieye::uds::E_ADDR_METHOD_FUNCTION,
                            },
                            .sessionAccessTypeList = {
                                minieye::uds::E_SESSION_TYPE_DEFAULT,
                                minieye::uds::E_SESSION_TYPE_EXTENDED,
                            },
                            .securityAccessLevelList = {
                                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                            },
                        },
                    },
                },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_COMM_CTRL_TYPE_DISABLE_RX_DISABLE_TX,
            .subFuncList = {
                {
                    .commType = minieye::uds::E_FLOW_COMM_TYPE_APP_MESSAGE,
                        .commChn = 0x00,
                        .hdler = []() -> minieye::uds::FlowCommCtrlHdlerRet_t {
                            return CommCtrl::Chn_0x00_DisableRxDisableTx_AppMessage_Hdler();
                        },
                        .condition = {
                            .addrMethodList = {
                                minieye::uds::E_ADDR_METHOD_LOGICAL,
                                minieye::uds::E_ADDR_METHOD_FUNCTION,
                            },
                            .sessionAccessTypeList = {
                                minieye::uds::E_SESSION_TYPE_DEFAULT,
                                minieye::uds::E_SESSION_TYPE_EXTENDED,
                            },
                            .securityAccessLevelList = {
                                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                            },
                        },
                },
                {
                    .commType = minieye::uds::E_FLOW_COMM_TYPE_NETWORK_MNGER_MESSAGE,
                    .commChn = 0x00,
                    .hdler = []() -> minieye::uds::FlowCommCtrlHdlerRet_t {
                        return CommCtrl::Chn_0x00_DisableRxDisableTx_NetworkMessge_Hdler();
                    },
                    .condition = {
                        .addrMethodList = {
                            minieye::uds::E_ADDR_METHOD_LOGICAL,
                            minieye::uds::E_ADDR_METHOD_FUNCTION,
                        },
                        .sessionAccessTypeList = {
                            minieye::uds::E_SESSION_TYPE_DEFAULT,
                            minieye::uds::E_SESSION_TYPE_EXTENDED,
                        },
                        .securityAccessLevelList = {
                            minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                            minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                        },
                    },
                },
                {
                    .commType = minieye::uds::E_FLOW_COMM_TYPE_APP_AND_NETWORK_MNGER_MESSAGE,
                    .commChn = 0x00,
                    .hdler = []() -> minieye::uds::FlowCommCtrlHdlerRet_t {
                        return CommCtrl::Chn_0x00_DisableRxDisableTx_AppAndNetworkMessge_Hdler();
                    },
                    .condition = {
                        .addrMethodList = {
                            minieye::uds::E_ADDR_METHOD_LOGICAL,
                            minieye::uds::E_ADDR_METHOD_FUNCTION,
                        },
                        .sessionAccessTypeList = {
                            minieye::uds::E_SESSION_TYPE_DEFAULT,
                            minieye::uds::E_SESSION_TYPE_EXTENDED,
                        },
                        .securityAccessLevelList = {
                            minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                            minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                        },
                    },
                },
            },
        },
    },
};

/////////////////////////////// UdsFlowDid ///////////////////////////////

static minieye::uds::UdsFlowReadDidCfg_t kReadDidCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_DEFAULT,
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdlerList = {
        {
            .did = 0xF187,
                .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                    return DID::Data_0xF187_Read_Hdler(value);
                },
                .condition = {
                    .addrMethodList = {
                        minieye::uds::E_ADDR_METHOD_LOGICAL,
                        minieye::uds::E_ADDR_METHOD_FUNCTION,
                    },
                    .sessionAccessTypeList = {
                        minieye::uds::E_SESSION_TYPE_DEFAULT,
                        minieye::uds::E_SESSION_TYPE_EXTENDED,
                        minieye::uds::E_SESSION_TYPE_OTA,
                    },
                    .securityAccessLevelList = {
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                    },
                },
        },
        {
            .did = 0xF18A,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF18A_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF197,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF197_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF193,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF193_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF195,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF195_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF18C,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF18C_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF190,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF190_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF188,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF188_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF191,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF191_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF18B,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF18B_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF15A,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF15A_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF103,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF103_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF186,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF186_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xDF01,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xDF01_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xD0D3,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xD0D3_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF1A0,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF1A0_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF1A1,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF1A1_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xD43D,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xD43D_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF101,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF101_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xD43E,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xD43E_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF1F1,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF1F1_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF1F2,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF1F2_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF12A,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF12A_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF12B,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF12B_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF180,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xF180_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xCF00,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xCF00_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xCF01,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xCF01_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xCF02,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xCF02_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xCF03,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0xCF03_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0x4F02,
            .hdler = [](std::vector<char> & value) -> minieye::uds::FlowReadDidHdlerRet_t {
                return DID::Data_0x4F02_Read_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_DEFAULT,
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {},
            },
        },
    },
};
static minieye::uds::UdsFlowReadDidListCfg_t kReadDidListCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_DEFAULT,
                minieye::uds::E_SESSION_TYPE_OTA,
                minieye::uds::E_SESSION_TYPE_EXTENDED,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_4,
            },
        },
    },
    .hdler = [](const std::vector<uint16_t> & didList, std::map<uint16_t, std::vector<char>> & valueList) -> bool {
        return DID::ReadList_Hdler(didList, valueList);
    }
};
static minieye::uds::UdsFlowWriteDidCfg_t kWriteDidCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdlerList = {
        {
            .did = 0xF190,
                .dataLen = 17,
                .hdler = [](const std::vector<char> & value) -> minieye::uds::FlowWriteDidHdlerRet_t {
                    return DID::Data_0xF190_Write_Hdler(value);
                },
                .condition = {
                    .addrMethodList = {
                        minieye::uds::E_ADDR_METHOD_LOGICAL,
                        minieye::uds::E_ADDR_METHOD_FUNCTION,
                    },
                    .sessionAccessTypeList = {
                        minieye::uds::E_SESSION_TYPE_EXTENDED,
                    },
                    .securityAccessLevelList = {
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    },
                },
        },
        {
            .did = 0xF15A,
            .dataLen = 9,
            .hdler = [](const std::vector<char> & value) -> minieye::uds::FlowWriteDidHdlerRet_t {
                return DID::Data_0xF15A_Write_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF103,
            .dataLen = 9,
            .hdler = [](const std::vector<char> & value) -> minieye::uds::FlowWriteDidHdlerRet_t {
                return DID::Data_0xF103_Write_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .did = 0xF101,
            .dataLen = 12,
            .hdler = [](const std::vector<char> & value) -> minieye::uds::FlowWriteDidHdlerRet_t {
                return DID::Data_0xF101_Write_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .did = 0x4F00,
            .dataLen = 18,
            .hdler = [](const std::vector<char> & value) -> minieye::uds::FlowWriteDidHdlerRet_t {
                return DID::Data_0x4F00_Write_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .did = 0x4F01,
            .dataLen = 3,
            .hdler = [](const std::vector<char> & value) -> minieye::uds::FlowWriteDidHdlerRet_t {
                return DID::Data_0x4F01_Write_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .did = 0x4F02,
            .dataLen = 1,
            .hdler = [](const std::vector<char> & value) -> minieye::uds::FlowWriteDidHdlerRet_t {
                return DID::Data_0x4F02_Write_Hdler(value);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                    minieye::uds::E_ADDR_METHOD_FUNCTION,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
    },
};

/////////////////////////////// UdsFlowDtc ///////////////////////////////

static minieye::uds::UdsFlowCtrlDtcSettingCfg_t kDtcSettingCtrlCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_EXTENDED,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
            },
        },
    },
    .startDtcUpdatingHdlerCfg = {
        .hdler = []() -> minieye::uds::FlowCtrlDtcSettingHdlerRet {
            return DTC::Ctrl_StartStatusUpdating_Hdler();
        },
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_EXTENDED,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
            },
        },
    },
    .stopDtcUpdatingHdlerCfg = {
        .hdler = []() -> minieye::uds::FlowCtrlDtcSettingHdlerRet {
            return DTC::Ctrl_StopStatusUpdating_Hdler();
        },
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_EXTENDED,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
            },
        },
    },
    .hdlerList = {},
};
static minieye::uds::UdsFlowReadDiagInfoCfg_t kReadDiagInfoCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_DEFAULT,
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .dtcStatusAvailabilityMask = 0x09,
    .dtcFormatIdentifier = minieye::uds::E_DTC_FORMAT_IDENTIFIER_ISO_14229_1,
    .reportNumberOfDtcByStatusMaskCondition = {
        .addrMethodList = {
            minieye::uds::E_ADDR_METHOD_LOGICAL,
            minieye::uds::E_ADDR_METHOD_FUNCTION,
        },
        .sessionAccessTypeList = {
            minieye::uds::E_SESSION_TYPE_DEFAULT,
            minieye::uds::E_SESSION_TYPE_EXTENDED,
            minieye::uds::E_SESSION_TYPE_OTA,
        },
        .securityAccessLevelList = {
            minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
            minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
            minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
        },
    },
    .reportDtcByStatusMaskCondition = {
        .addrMethodList = {
            minieye::uds::E_ADDR_METHOD_LOGICAL,
            minieye::uds::E_ADDR_METHOD_FUNCTION,
        },
        .sessionAccessTypeList = {
            minieye::uds::E_SESSION_TYPE_DEFAULT,
            minieye::uds::E_SESSION_TYPE_EXTENDED,
            minieye::uds::E_SESSION_TYPE_OTA,
        },
        .securityAccessLevelList = {
            minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
            minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
            minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
        },
    },
    .reportDtcSnapshotByNumberCondition = {
        .addrMethodList = {
            minieye::uds::E_ADDR_METHOD_LOGICAL,
            minieye::uds::E_ADDR_METHOD_FUNCTION,
        },
        .sessionAccessTypeList = {
            minieye::uds::E_SESSION_TYPE_DEFAULT,
            minieye::uds::E_SESSION_TYPE_EXTENDED,
            minieye::uds::E_SESSION_TYPE_OTA,
        },
        .securityAccessLevelList = {
            minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
            minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
            minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
        },
    },
    .reportDtcAllSnapshotCondition = {
        .addrMethodList = {},
        .sessionAccessTypeList = {},
        .securityAccessLevelList = {},
    },
    .reportDtcExtDataByNumberCondition = {
        .addrMethodList = {},
        .sessionAccessTypeList = {},
        .securityAccessLevelList = {},
    },
    .reportDtcAllExtDataCondition = {
        .addrMethodList = {},
        .sessionAccessTypeList = {},
        .securityAccessLevelList = {},
    },
    .reportSupportedDtcCondition = {
        .addrMethodList = {
            minieye::uds::E_ADDR_METHOD_LOGICAL,
            minieye::uds::E_ADDR_METHOD_FUNCTION,
        },
        .sessionAccessTypeList = {
            minieye::uds::E_SESSION_TYPE_DEFAULT,
            minieye::uds::E_SESSION_TYPE_EXTENDED,
            minieye::uds::E_SESSION_TYPE_OTA,
        },
        .securityAccessLevelList = {
            minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
            minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
            minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
        },
    },
};
static minieye::uds::UdsFlowClrDiagInfoCfg_t kClrDiagInfoCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_DEFAULT,
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .clrAllDiagInfoHdlerCfg = {
        .preCondChkHdler = []() -> minieye::uds::FlowClrDiagInfoPreCondChkHdlerHdlerRet_t {
            return DTC::ClearAll_PreCndChkHdler();
        },
        .hdler = []() -> bool {
            return DTC::ClearAll_Hdler();
        },
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_DEFAULT,
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdlerList = {},
};

/////////////////////////////// UdsFlowReset ///////////////////////////////

static minieye::uds::UdsFlowResetCfg_t kResetCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_DEFAULT,
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hardResetHdlerCfg = {
        .preCondChkHdler = []() -> minieye::uds::FlowResetPreCondChkHdlerHdlerRet_t {
            return Reset::HardResetPreCondChk();
        },
        .resetHdler = []() -> bool {
            return Reset::HardResetHdler();
        },
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
                minieye::uds::E_ADDR_METHOD_FUNCTION,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_DEFAULT,
                minieye::uds::E_SESSION_TYPE_EXTENDED,
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .softResetHdlerCfg = {
        .preCondChkHdler = []() -> minieye::uds::FlowResetPreCondChkHdlerHdlerRet_t {
            return minieye::uds::E_FLOW_RESET_PRE_COND_CHK_HDLER_RET_SUCCESS;
        },
        .resetHdler = nullptr,
        .condition = {
            .addrMethodList = {},
            .sessionAccessTypeList = {},
            .securityAccessLevelList = {},
        },
    },
    .hdlerList = {},
};

/////////////////////////////// UdsFlowRid ///////////////////////////////

static minieye::uds::UdsFlowRoutineCtrlCfg_t kRoutineCtrlCfg = {
    .base = {
        .condition = {
            .addrMethodList = {},
            .sessionAccessTypeList = {},
            .securityAccessLevelList = {},
        },
    },
    .hdlerList = {
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
                .rid = 0x0205,
                .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                    uint16_t & routineInfo,
                    std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                    return RoutineCtrl::Data_0x0205_Start_ModeHdler(param, routineInfo, statusRecord);
                },
                .condition = {
                    .addrMethodList = {
                        minieye::uds::E_ADDR_METHOD_LOGICAL,
                    },
                    .sessionAccessTypeList = {
                        minieye::uds::E_SESSION_TYPE_OTA,
                    },
                    .securityAccessLevelList = {
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                        minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                    },
                },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0206,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0206_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0207,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0207_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_0,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0208,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0208_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_REQUEST_RESULT,
            .rid = 0x0208,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0208_RequestResult_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0209,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0209_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {},
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0210,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0210_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0211,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0211_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0212,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0212_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0761,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0761_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {},
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_REQUEST_RESULT,
            .rid = 0x0761,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0761_RequestResult_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {},
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x0213,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x0213_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_OTA,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x5356,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x5356_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x5357,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x5357_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x5366,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x5366_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_REQUEST_RESULT,
            .rid = 0x5366,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x5366_RequestResult_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x5370,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x5370_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_START,
            .rid = 0x5367,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x5367_Start_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
        {
            .ctrlType = minieye::uds::E_FLOW_ROUTINE_CTRL_TYPE_REQUEST_RESULT,
            .rid = 0x5367,
            .hdler = [](const minieye::uds::FlowRoutineCtrlHdlerParam_t & param,
                uint16_t & routineInfo,
                std::vector<char> & statusRecord) -> minieye::uds::FlowRoutineCtrlHdlerRet_t {
                return RoutineCtrl::Data_0x5367_RequestResult_ModeHdler(param, routineInfo, statusRecord);
            },
            .condition = {
                .addrMethodList = {
                    minieye::uds::E_ADDR_METHOD_LOGICAL,
                },
                .sessionAccessTypeList = {
                    minieye::uds::E_SESSION_TYPE_EXTENDED,
                },
                .securityAccessLevelList = {
                    minieye::uds::E_SECURITY_ACCESS_LEVEL_1,
                },
            },
        },
    },
};

/////////////////////////////// UdsFlowTransfer ///////////////////////////////

static minieye::uds::UdsFlowTransferCfg_t kTransferCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdler = [](const minieye::uds::TransferFileInfo_t & fileInfo, int32_t dlSize, int32_t totSize) -> bool {
        return Transfer::Data_Hdler(fileInfo, dlSize, totSize);
    },
};
static minieye::uds::UdsFlowRequestDownloadCfg_t kRequestDownloadCfg = {
    .base = {
        .condition = {
            .addrMethodList = {},
            .sessionAccessTypeList = {},
            .securityAccessLevelList = {},
        },
    },
    .hdler = [](int64_t memoryAddr, int32_t memorySize, uint8_t compression, uint8_t encryption,
        std::string & realStorageFilePath) -> minieye::uds::FlowRequestDownloadHdlerRet_t {
        return minieye::uds::E_FLOW_REQUEST_DOWNLOAD_HDLER_RET_GR_ERROR;
    },
};
static minieye::uds::UdsFlowRequestTransferCfg_t kRequestFileTransferCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdler = [](const minieye::uds::TransferFileInfo_t & fileInfo) -> minieye::uds::FlowRequestTransferHdlerRet_t {
        return Transfer::RequestFile_Hdler(fileInfo);
    },
};
static minieye::uds::UdsFlowRequestTransferExitCfg_t kRequestTransferExitCfg = {
    .base = {
        .condition = {
            .addrMethodList = {
                minieye::uds::E_ADDR_METHOD_LOGICAL,
            },
            .sessionAccessTypeList = {
                minieye::uds::E_SESSION_TYPE_OTA,
            },
            .securityAccessLevelList = {
                minieye::uds::E_SECURITY_ACCESS_LEVEL_2,
            },
        },
    },
    .hdler = [](const minieye::uds::TransferFileInfo_t & fileInfo,
        const std::vector<char> & paramRecord) -> bool {
        return Transfer::RequestExit_Hdler(fileInfo, paramRecord);
    },
};

/////////////////////////////// Launcher ///////////////////////////////

/// Launcher IMPL --------------------------------------------------
static minieye::doip::DoIPParamCfg_t _Impl_GetDoIPParam() {
    minieye::doip::DoIPParamCfg_t param;
    param.iface = "eth0.5";
    param.certFilePath = "";
    param.keyFilePath = "";
    param.tcpLlistenerPort = 13400;
    param.udpLlistenerPort = 13400;
    param.bcastIpAddr = "************";
    param.bcastPort = 13400;
    param.logicalEcuAddr = 0x192B;
    param.logicalFunctionAddr = 0xE400;
    param.logicalTesterAddrList = {
        0x0E80,
        0x0E81,
        0x0F00,
        0x0F01,
    };
    param.vehicleIdentifierBroadcastIntervalTms = 500;
    param.vehicleIdentifierBroadcastCount = 3;
    param.minUdpClientPort = 49152;
    param.maxUdpClientPort = 65535;
    param.maxTcpClientNumber = 2;
    param.noActedIoTmoutMs = 2000;
    param.actedIoTmoutMs = 300000;
    param.waitAliveChkRspIoTmoutMs = 500;
    param.DoIPVer = 0x02;
    param.needVehicleIdentifierSyncStatusField = false;
    return param;
}
static minieye::uds::LibUDS_InitParam_t _Impl_GetUdsParam() {
    auto param = minieye::uds::LibUDS_GetDefaultInitParam();
    param.udsParamCfg.supportedSessionTypeList = {
        minieye::uds::E_SESSION_TYPE_DEFAULT,
        minieye::uds::E_SESSION_TYPE_EXTENDED,
        minieye::uds::E_SESSION_TYPE_OTA,
    };
    param.udsParamCfg.s3 = 5000;
    param.udsParamCfg.p2 = 50;
    param.udsParamCfg.p2x = 5000;
    param.udsParamCfg.resetTakeTmsec = 30;
    param.udsParamCfg.maxNumberOfBlockLen = 1048576;
    param.udsParamCfg.securityAccessRetryMaxNum = 3;
    param.udsParamCfg.securityAccessRetryIntervalTms = 10000;
    param.udsParamCfg.dtcFileDir = "/app_param/doip/dtc";
    param.udsParamCfg.dirPathMap = {
        {
            "/userdata/fota/",
            "/ota"
        },
    };
    return param;
}
static minieye::uds::UdsFlowCfg_t _Impl_GetUdsFlowCfg() {
    minieye::uds::UdsFlowCfg_t cfg;
    cfg.flowSessionCtrlCfg = kSessionCtrlCfg;
    cfg.flowResetCfg = kResetCfg;
    cfg.flowClrDiagInfoCfg = kClrDiagInfoCfg;
    cfg.flowReadDiagInfoCfg = kReadDiagInfoCfg;
    cfg.flowCtrlDtcSettingCfg = kDtcSettingCtrlCfg;
    cfg.flowReadDidCfg = kReadDidCfg;
    cfg.flowReadDidListCfg = kReadDidListCfg;
    cfg.flowWriteDidCfg = kWriteDidCfg;
    cfg.flowRequestSeedCfg = kRequestSeedCfg;
    cfg.flowSendKeyCfg = kSendKeyCfg;
    cfg.flowCommCtrlCfg = kCommCtrlCfg;
    cfg.flowRoutineCtrlCfg = kRoutineCtrlCfg;
    cfg.flowRequestTransferCfg = kRequestFileTransferCfg;
    cfg.flowTransferCfg = kTransferCfg;
    cfg.flowRequestTransferExitCfg = kRequestTransferExitCfg;
    return cfg;
}
/// Launcher API --------------------------------------------------
minieye::doip::DoIPParamCfg_t & Launcher::GetDoIPParam() {
    static auto param = _Impl_GetDoIPParam();
    return param;
}
minieye::uds::LibUDS_InitParam_t & Launcher::GetUdsParam() {
    static auto param = _Impl_GetUdsParam();
    return param;
}
minieye::uds::UdsFlowCfg_t & Launcher::GetUdsFlowCfg() {
    static auto cfg = _Impl_GetUdsFlowCfg();
    return cfg;
}

// parasoft-instrumentation on

