
/*
 * DOIP CODE GENERATOR BY MINIEYE, DO NOT MODIFY THIS FILE!
 * 
 *                 @ MINIEYE CopyRight  
 *
 * Generated Date: 2025/8/22 15:41:15
 * See More infos: (TODO)
 */

#include <LibUDS.h>
#include <LibUdsOnDoIP.h>
#include <IPC_matrix_Middleware.h>

namespace minieye {
namespace dolink {
namespace interface {


/////////////////////////////// FaultStorage ///////////////////////////////

struct Fault {
    typedef struct MapInfo {
        std::function<int32_t(can_obj_ipc_matrix_middleware_h_t * , uint8_t * ) > func;
        // fault num
        uint16_t faultNum;
        // 上一次的故障状态
        uint8_t preDtcStatus;
        // 老化次数
        int32_t dtcAgingNum = 10;
        // 快照记录号
        std::vector<uint8_t> snapshotRecordNums = {
            0x01
        };
        std::vector<uint8_t> extDataRecordNums = {
            0x01,
            0x02
        };
    }
    DtcMapInfo_t;
    static std::map<uint32_t, Fault::DtcMapInfo_t> & GetMap();
};

/////////////////////////////// UdsFlowAccess ///////////////////////////////

struct Access {
    ////// <<< Session -----------------------------------------------------
    static bool Session_Default_Hdler();
    static bool Session_Extended_Hdler();
    static bool Session_OTA_Hdler();
    ////// <<< Security -----------------------------------------------------
    static minieye::uds::FlowRequestSeedHdlerRet_t Security_RequestSeed_0x01_Hdler(std::vector<char> & seed);
    static minieye::uds::FlowSendKeyHdlerRet_t Security_SendKey_0x02_Hdler(const std::vector<char> & seed,
        const std::vector<char> & sendKey,
            minieye::uds::SecurityAccessLevel_t & securityAccessLevel);
    static minieye::uds::FlowRequestSeedHdlerRet_t Security_RequestSeed_0x03_Hdler(std::vector<char> & seed);
    static minieye::uds::FlowSendKeyHdlerRet_t Security_SendKey_0x04_Hdler(const std::vector<char> & seed,
        const std::vector<char> & sendKey,
            minieye::uds::SecurityAccessLevel_t & securityAccessLevel);
};

/////////////////////////////// UdsFlowCommCtrl ///////////////////////////////

struct CommCtrl {
    static minieye::uds::FlowCommCtrlHdlerRet_t Chn_0x00_EnableRxEnableTx_AppMessage_Hdler();
    static minieye::uds::FlowCommCtrlHdlerRet_t Chn_0x00_EnableRxEnableTx_NetworkMessge_Hdler();
    static minieye::uds::FlowCommCtrlHdlerRet_t Chn_0x00_EnableRxEnableTx_AppAndNetworkMessge_Hdler();
    static minieye::uds::FlowCommCtrlHdlerRet_t Chn_0x00_DisableRxDisableTx_AppMessage_Hdler();
    static minieye::uds::FlowCommCtrlHdlerRet_t Chn_0x00_DisableRxDisableTx_NetworkMessge_Hdler();
    static minieye::uds::FlowCommCtrlHdlerRet_t Chn_0x00_DisableRxDisableTx_AppAndNetworkMessge_Hdler();
};

/////////////////////////////// UdsFlowDid ///////////////////////////////

struct DID {
    ////// <<< Read -----------------------------------------------------
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF187_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF18A_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF197_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF193_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF195_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF18C_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF190_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF188_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF191_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF18B_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF15A_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF103_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF186_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xDF01_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xD0D3_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF1A0_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF1A1_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xD43D_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF101_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xD43E_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF1F1_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF1F2_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF12A_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF12B_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xF180_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xCF00_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xCF01_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xCF02_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0xCF03_Read_Hdler(std::vector<char> & value);
    static minieye::uds::FlowReadDidHdlerRet_t Data_0x4F02_Read_Hdler(std::vector<char> & value);
    ////// <<< Read List -------------------------------------------------
    static bool ReadList_Hdler(const std::vector<uint16_t> & didList,
        std::map<uint16_t, std::vector<char>> & valueList);
    ////// <<< Write -----------------------------------------------------
    static minieye::uds::FlowWriteDidHdlerRet_t Data_0xF190_Write_Hdler(const std::vector<char> & value);
    static minieye::uds::FlowWriteDidHdlerRet_t Data_0xF15A_Write_Hdler(const std::vector<char> & value);
    static minieye::uds::FlowWriteDidHdlerRet_t Data_0xF103_Write_Hdler(const std::vector<char> & value);
    static minieye::uds::FlowWriteDidHdlerRet_t Data_0xF101_Write_Hdler(const std::vector<char> & value);
    static minieye::uds::FlowWriteDidHdlerRet_t Data_0x4F00_Write_Hdler(const std::vector<char> & value);
    static minieye::uds::FlowWriteDidHdlerRet_t Data_0x4F01_Write_Hdler(const std::vector<char> & value);
    static minieye::uds::FlowWriteDidHdlerRet_t Data_0x4F02_Write_Hdler(const std::vector<char> & value);
};

/////////////////////////////// UdsFlowDtc ///////////////////////////////

struct DTC {
    ////// <<< Ctrl -----------------------------------------------------
    static minieye::uds::FlowCtrlDtcSettingHdlerRet Ctrl_StartStatusUpdating_Hdler();
    static minieye::uds::FlowCtrlDtcSettingHdlerRet Ctrl_StopStatusUpdating_Hdler();
    ////// <<< Clear -----------------------------------------------------
    static minieye::uds::FlowClrDiagInfoPreCondChkHdlerHdlerRet_t ClearAll_PreCndChkHdler();
    static bool ClearAll_Hdler();
};

/////////////////////////////// UdsFlowReset ///////////////////////////////

struct Reset {
    static minieye::uds::FlowResetPreCondChkHdlerHdlerRet_t HardResetPreCondChk();
    static bool HardResetHdler();
};

/////////////////////////////// UdsFlowRid ///////////////////////////////

struct RoutineCtrl {
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0205_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0206_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0207_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0208_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0208_RequestResult_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0209_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0210_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0211_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0212_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0761_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0761_RequestResult_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x0213_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x5356_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x5357_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x5366_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x5366_RequestResult_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x5370_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x5367_Start_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
    static minieye::uds::FlowRoutineCtrlHdlerRet_t Data_0x5367_RequestResult_ModeHdler(
        const minieye::uds::FlowRoutineCtrlHdlerParam_t & param, uint16_t & routineInfo, std::vector<char> & statusRecord);
};

/////////////////////////////// UdsFlowTransfer ///////////////////////////////

struct Transfer {
    static bool Data_Hdler(
        const minieye::uds::TransferFileInfo_t & fileInfo, int32_t dlSize, int32_t totSize);
    static minieye::uds::FlowRequestTransferHdlerRet_t RequestFile_Hdler(
        const minieye::uds::TransferFileInfo_t & fileInfo);
    static bool RequestExit_Hdler(
        const minieye::uds::TransferFileInfo_t & fileInfo,
            const std::vector<char> & paramRecord);
};

/////////////////////////////// Launcher ///////////////////////////////

struct Launcher {
    static minieye::doip::DoIPParamCfg_t & GetDoIPParam();
    static minieye::uds::LibUDS_InitParam_t & GetUdsParam();
    static minieye::uds::UdsFlowCfg_t & GetUdsFlowCfg();
};

} // interface
} // dolink
} // minieye
