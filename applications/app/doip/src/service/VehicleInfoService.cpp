/*
 * Copyright [2025] MINIEYE
 * Descripttion : 车辆信息服务
 * Author       : zhouyuxuan
 * Date         : 2025-06-03
 */

#include "VehicleInfoService.h"
#include "Launcher.h"

#define DID_SN               0xF18C
#define DID_VIN              0xF190
#define DID_FINGERPRINT      0xF15A
#define DID_ECU_VERSION_HW   0xF193
#define DID_ECU_VERSION_SOFT 0xF195

using namespace minieye::dolink;

VehicleInfoService::VehicleInfoService()
    : Service("VehicleInfoService")
    , mEcuVerHw({0x00, 0x01})
    , mEcuVerSw({'V', '3', '_', 0x00, 0x01, '_', 0x00, 0x03}) {

    RegisterCom(COMPONENT_FS());
    RegisterCom(COMPONENT_ISOTP());
    RegisterCom(COMPONENT_SYS_INFO_QUERY());

    COMPONENT_SYS_INFO_QUERY()->RegisterQueryCallback(&VehicleInfoService::OnProcQuery, this);
}

VehicleInfoService::~VehicleInfoService() = default;

bool VehicleInfoService::Start() {
    // zyx(25-6-20): 两个版本号在启动是传递给MCU
    SetVersion2MCU();

    GetQCraftVersion();

    LoadEnv();
    return true;
}

void VehicleInfoService::GetSN(std::vector<char>& data) {
    data.assign(18, 0xFF);

    if (!GetCom<IsotpService>()->DidGet(DID_SN, data)) {
        MLOG_E("Get SN failed!");
    }
}

bool VehicleInfoService::SetSN(const std::vector<char>& data) {
    return GetCom<IsotpService>()->DidSet(DID_SN, data);
}

void VehicleInfoService::GetVIN(std::vector<char>& data) {
    data.assign(17, 0xFF);

    if (!GetCom<IsotpService>()->DidGet(DID_VIN, data)) {
        MLOG_E("Get VIN failed!");
    }
}

bool VehicleInfoService::SetVIN(const std::vector<char>& data) {
    return GetCom<IsotpService>()->DidSet(DID_VIN, data);
}

void VehicleInfoService::GetFingerprint(std::vector<char>& data) {
    data.assign(9, 0);

    if (!GetCom<FileSysService>()->Read(DID_FINGERPRINT, data)) {
        MLOG_E("Get Fingerprint failed!");
    }
}

bool VehicleInfoService::SetFingerprint(const std::vector<char>& data) {
    return GetCom<FileSysService>()->Write(DID_FINGERPRINT, data);
}

void VehicleInfoService::GetEquipmentMatintenanceFingerprint(std::vector<char>& data) {
    data.assign(9, 0);

    if (!GetCom<FileSysService>()->Read(0xF103, data)) {
        MLOG_E("Get Equipment Matintenance Fingerprint failed!");
    }
}

bool VehicleInfoService::SetEquipmentMatintenanceFingerprint(const std::vector<char>& data) {
    return GetCom<FileSysService>()->Write(0xF103, data);
}

void VehicleInfoService::GetVehicleConfiguration(std::vector<char>& data) {
    data.assign({0x00, 0x00, 0x04, 0x72, 0x40, 0x01, 0x00, 0xA3, 0x00, 0x00, 0x00, 0x00});

    if (!GetCom<IsotpService>()->DidGet(0xF101, data)) {
        MLOG_E("Get Vehicle Configuration failed!");
    }
}

bool VehicleInfoService::SetVehicleConfiguration(const std::vector<char>& data) {
    return GetCom<IsotpService>()->DidSet(0xF101, data);
}

void VehicleInfoService::GetECUHardwareVersion(std::vector<char>& data) {
    data = mEcuVerHw;
}

void VehicleInfoService::GetECUSoftwareVersion(std::vector<char>& data) {
    data = mEcuVerSw;
}

void VehicleInfoService::GetECUManufactureDate(std::vector<char>& data) {
    data.assign({0, 0, 0});

    if (!GetCom<FileSysService>()->Read(0xF18B, data)) {
        MLOG_E("Get Manufacture Date failed!");
    }
}

bool VehicleInfoService::SetECUManufactureDate(const std::vector<char>& data) {
    return GetCom<FileSysService>()->Write(0xF18B, data);
}

void VehicleInfoService::GetEnv(std::vector<char>& data) {
    data.assign(1, static_cast<char>(mEnv));
}

void VehicleInfoService::SetEnv(EnvType_t env) {
    mEnv = env;
    StoreEnv();
}

void VehicleInfoService::SetVersion2MCU() {
    GetCom<IsotpService>()->DidSet(DID_ECU_VERSION_HW, mEcuVerHw);
    GetCom<IsotpService>()->DidSet(DID_ECU_VERSION_SOFT, mEcuVerSw);
}

void VehicleInfoService::GetQCraftVersion() {
    std::vector<char> version(32, 0);
    if (!GetCom<IsotpService>()->DidGet(0xD401, version)) {
        MLOG_E("Get QCraft version failed!");
    }
    system::file::CreateFile("/tmp/mcu_version");
    system::file::WriteFile("/tmp/mcu_version", version.data(), version.size(), 0);
}

void VehicleInfoService::LoadEnv() {
    std::vector<char> data;
    if (!GetCom<FileSysService>()->Read("env", data)) {
        MLOG_E("Read env failed! Use default env PP.");
        mEnv = E_ENV_TYPE_PP;
        return ;
    }
    mEnv = static_cast<EnvType_t>(data[0]);
}

void VehicleInfoService::StoreEnv() {
    std::vector<char> data(1, static_cast<char>(mEnv));
    if (!GetCom<FileSysService>()->Write("env", data)) {
        MLOG_E("Store env failed!");
    }
}

void VehicleInfoService::GetEnvSettings(std::vector<char>& data) {
    data.assign(1, static_cast<char>(mEnv));
}

void VehicleInfoService::OnProcQuery(SysInfoType type, std::vector<char>& data) {
    switch (type) {
        case E_SYS_INFO_TYPE_SN:
            GetSN(data);
            break;
        case E_SYS_INFO_TYPE_VIN:
            GetVIN(data);
            break;
        case E_SYS_INFO_TYPE_ENV:
            GetEnvSettings(data);
            break;
        default:
            MLOG_E("Unknown SysInfoType: {}", type);
    }
}