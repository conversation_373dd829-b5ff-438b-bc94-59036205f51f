/*
 * Copyright [2025] MINIEYE
 * Descripttion : ADACU密钥服务
 * Author       : zhouyuxuan
 * Date         : 2025-05-30
 */

#include "AdacuService.h"

#include <iomanip>

#include <mlog/Logging.h>
#include <mlog/HexDump.h>

#include <openssl/rand.h>
#include <openssl/err.h>
#include <openssl/evp.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <openssl/md5.h>

#include <nlohmann/json.hpp>

#include <hb_st_key_manager.h>

#include "Launcher.h"
#include "SSLHelper.hpp"

#define ADACU_KEY_LEN         16
#define ADACU_UTC_LEN         5
#define ADACU_AES_STORAGE_KEY "adacu-key"

#define ADACU_KEY_DIR_PATH     "/app_param/doip/adacu"
#define ADACU_KEY_PATH         ADACU_KEY_DIR_PATH "/key"    // old
#define ADACU_S_KEY_PATH       ADACU_KEY_DIR_PATH "/s_key"  // new 加密后
#define ADACU_EXPIRED_UTC_PATH ADACU_KEY_DIR_PATH "/expired_utc"
#define ADACU_AES_KEY_PATH     ADACU_KEY_DIR_PATH "/aes"
#define ADACU_VID_PATH         ADACU_KEY_DIR_PATH "/vid"

static const std::vector<uint8_t> kIV(16, 0x00);
static boost::asio::ssl::context kCtx(boost::asio::ssl::context::tlsv12_client);

using namespace minieye::dolink;

typedef struct KeyStorage {
    char key[ADACU_KEY_LEN];
    uint64_t utc;
} KeyStorage_t;

typedef struct KeyRspMsg {
    AdacuService::Result_t code;
    char key[ADACU_KEY_LEN];
    char utc[ADACU_UTC_LEN];
    uint8_t rslt;
    AdacuService::Result_t rslt_1;
} KeyRspMsg_t;

SSL_HANDLE_DEF(EVP_CIPHER_CTX)
SSL_HANDLE_DEF(BIO)

#define SSL_HANDLE_SUCCESSED(HANDLE)           \
    do {                                       \
        if (!HANDLE.IsValid()) {               \
            MLOG_E("Error creating " #HANDLE); \
            return -2;                         \
        }                                      \
    } while (0)

// AES-128-CBC 加密
static int AES_CBC_Encrypt(const std::vector<uint8_t>& rawKey,
                           const std::vector<uint8_t>& key,
                           const std::vector<uint8_t>& iv,
                           std::vector<uint8_t>& outKey) {
    SSL_HANDLE(EVP_CIPHER_CTX) ctx = EVP_CIPHER_CTX_new();
    SSL_HANDLE_SUCCESSED(ctx);

    // 初始化加密操作
    if (EVP_EncryptInit_ex(ctx.Get(), EVP_aes_128_cbc(), nullptr, key.data(), iv.data()) != 1) {
        return -1;
    }

    uint8_t buf[256];
    int outKeyLen, len;

    // 提供要加密的数据
    if (EVP_EncryptUpdate(ctx.Get(), buf, &len, rawKey.data(), rawKey.size()) != 1) {
        return -1;
    }
    outKeyLen = len;

    // 完成加密，处理可能的填充
    if (EVP_EncryptFinal_ex(ctx.Get(), buf + len, &len) != 1) {
        return -1;
    }
    outKeyLen += len;

    outKey.assign(buf, buf + outKeyLen);

    return 0;
}

// AES-128-CBC 解密
static int AES_CBC_Decrypt(const std::vector<uint8_t>& rawKey,
                           const std::vector<uint8_t>& key,
                           const std::vector<uint8_t>& iv,
                           std::vector<uint8_t>& outKey) {
    SSL_HANDLE(EVP_CIPHER_CTX) ctx = EVP_CIPHER_CTX_new();
    SSL_HANDLE_SUCCESSED(ctx);

    // 初始化解密操作
    if (EVP_DecryptInit_ex(ctx.Get(), EVP_aes_128_cbc(), NULL, key.data(), iv.data()) != 1) {
        return -1;
    }

    uint8_t buf[256];
    int outKeyLen, len;

    // 提供要解密的数据
    if (EVP_DecryptUpdate(ctx.Get(), buf, &len, rawKey.data(), rawKey.size()) != 1) {
        return -1;
    }
    outKeyLen = len;

    // 完成解密，处理可能的填充
    if (EVP_DecryptFinal_ex(ctx.Get(), buf + len, &len) != 1) {
        return -1;
    }
    outKeyLen += len;

    outKey.assign(buf, buf + outKeyLen);

    return 0;
}

// Base64 编码
static int Base64Encode(const std::vector<uint8_t>& rawKey, std::vector<uint8_t>& keyOut) {
    SSL_HANDLE(BIO) b64 = BIO_new(BIO_f_base64());
    SSL_HANDLE(BIO) bio = BIO_new(BIO_s_mem());

    auto pBio = BIO_push(*b64, *bio);

    // 禁止换行
    BIO_set_flags(pBio, BIO_FLAGS_BASE64_NO_NL);

    // 写入数据
    BIO_write(pBio, rawKey.data(), rawKey.size());
    BIO_flush(pBio);

    // 获取内存缓冲区
    BUF_MEM* buf;
    BIO_get_mem_ptr(pBio, &buf);

    keyOut.assign(buf->data, buf->data + buf->length);
    return 0;
}

// Base64 解码
static int Base64Decode(const std::vector<uint8_t>& rawKey, std::vector<uint8_t>& keyOut) {
    uint8_t* buf = (uint8_t*)malloc(rawKey.size());
    memset(buf, 0, rawKey.size());

    SSL_HANDLE(BIO) b64 = BIO_new(BIO_f_base64());
    SSL_HANDLE(BIO) bio = BIO_new_mem_buf(rawKey.data(), rawKey.size());
    auto pBio           = BIO_push(*b64, *bio);

    // 禁止换行
    BIO_set_flags(pBio, BIO_FLAGS_BASE64_NO_NL);

    // 读取解码后的数据
    int len = BIO_read(pBio, buf, rawKey.size());

    keyOut.assign(buf, buf + len);

    // 清理 BIO 和缓冲区
    free(buf);
    return 0;
}

static std::string SigCal(const std::vector<uint8_t>& bin) {
    std::stringstream ss;
    uint8_t md5[MD5_DIGEST_LENGTH];

    ss << std::hex << std::setfill('0');
    for (unsigned char byte : bin) {
        ss << std::setw(2) << static_cast<int>(byte);
    }
    MD5(reinterpret_cast<const uint8_t*>(ss.str().data()), ss.str().size(), md5);

    ss.str("");
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
        ss << std::setw(2) << static_cast<int>(md5[i]);
    }
    return ss.str();
}

////////////////////////////////// AdacuService //////////////////////////////////

AdacuService::AdacuService()
    : Service("AdacuService") {
    mAesStorage = std::make_unique<hobot::AESKey>(hobot::AESKey::E_AES_TYPE_128, "doip-adacu");

    OpenSSL_add_all_algorithms();
    ERR_load_crypto_strings();

    RegisterCom(COMPONENT_ISOTP());
    RegisterCom(COMPONENT_SYS_INFO_QUERY());

    COMPONENT_SYS_INFO_QUERY()->RegisterQueryCallback(&AdacuService::OnProcQuery, this);
}

AdacuService::~AdacuService() {
    EVP_cleanup();
    ERR_free_strings();
}

bool AdacuService::Start() {
    minieye::system::file::CreateDir(ADACU_KEY_DIR_PATH);

    InitAesKey();

    KeyStorageRead();

    mRunning      = true;
    mVerifyWorker = std::thread(&AdacuService::VerifyValidWorker, this);
    mGetVidWorker = std::thread(&AdacuService::GetVidWorker, this);

    return true;
}

void AdacuService::Stop() {
    mRunning = false;
    mVerifyWorker.join();
    mGetVidWorker.join();
}

bool AdacuService::Register(const std::vector<char>& sn) {
    std::vector<char> rsp;

    if (!GetCom<IsotpService>()->RoutineRequest(0x0761, 0x01, sn, rsp)) {
        MLOG_E("Register ADACU key failed, err: isotp request err!");
        SetStatus(E_STATUS_REGISTER_BAD);
        return false;
    }

    if (rsp.size() == 0) {
        MLOG_E("Register ADACU key failed, err: Got bad msg length!");
        SetStatus(E_STATUS_REGISTER_BAD);
        return false;
    }

    SetStatus(rsp[0] ? E_STATUS_REGISTER_BAD : E_STATUS_REGISTERING);
    return rsp[0];
}

bool AdacuService::QueryProgress(Result_t& result) {
    std::vector<char> rsp;

    if (!GetCom<IsotpService>()->RoutineRequest(0x0761, 0x03, {}, rsp)) {
        MLOG_E("Isotp send failed!");
        SetStatus(E_STATUS_REGISTER_BAD);
        return false;
    }

    MLOG_I("Got rsp: {:Xn}", minieye::mlog::toHex(rsp));

    if (rsp.size() < 1) {
        MLOG_E("Query ADACU key status failed, err: Got bad msg length!");
        SetStatus(E_STATUS_REGISTER_BAD);
        return false;
    }

    auto msg = reinterpret_cast<KeyRspMsg_t*>(rsp.data());

    result = msg->code;
    if (result >= E_RESULT_NOT_REGISTER && result < E_RESULT_TBOX_GET_KEY_OK) {
        MLOG_W("Got msg, but result is not ok!");
        SetStatus(E_STATUS_REGISTER_BAD);
        return false;
    }

    std::vector<uint8_t> key(std::begin(msg->key), std::end(msg->key));

    mUtc = ((static_cast<uint64_t>(msg->utc[0]) << 32) & 0x000000FF00000000) |
           ((static_cast<uint64_t>(msg->utc[1]) << 24) & 0x00000000FF000000) |
           ((static_cast<uint64_t>(msg->utc[2]) << 16) & 0x0000000000FF0000) |
           ((static_cast<uint64_t>(msg->utc[3]) << 8) & 0x000000000000FF00) |
           ((static_cast<uint64_t>(msg->utc[4]) << 0) & 0x00000000000000F);

    MLOG_I("Got expired ts: {}", mUtc);

    // SetStatus(rsp[1 + ADACU_KEY_LEN + ADACU_UTC_LEN + 1] ? E_STATUS_REGISTER_OK : E_STATUS_REGISTERING);
    if (msg->code == E_RESULT_TBOX_GET_KEY_OK) {
        SetStatus(E_STATUS_REGISTER_OK);
        KeyStorageSave(key);
    } else if (msg->code == E_RESULT_REGISTERING) {
        SetStatus(E_STATUS_REGISTERING);
    } else {
        SetStatus(E_STATUS_REGISTER_BAD);
    }

    result = msg->rslt_1;
    return msg->rslt;
}

int AdacuService::GetKey(std::vector<uint8_t>& key) {
    // 安全存储启用判断
    if (hobot::CryptoKey::LifecycleChk() == 0) {
        if (mAesStorage->Load(ADACU_AES_STORAGE_KEY, key) != 0) {
            MLOG_E("Read from RPMB failed!");
            return -1;
        }
    }
    MLOG_W("RPMB is not available! Read from mem...");

    if (AES_CBC_Decrypt(mKey, mAesKey, kIV, key) != 0) {
        MLOG_E("Key decrypted failed!");
        return -1;
    }

    return 0;
}

void AdacuService::KeyStorageRead() {
    if (LoadKeyStorageFromOld() == 0) {
        SetStatus(E_STATUS_REGISTER_OK);
        return;
    }
    MLOG_W("Old key storage not existed! Try read from local...");
    if (LoadKeyStorageFromLocal() == 0) {
        SetStatus(E_STATUS_REGISTER_OK);
        return;
    }
    MLOG_W("Local key storage not existed! Try read from RPMB...");
    if (LoadKeyStorageFromRPMB() == 0) {
        SetStatus(E_STATUS_REGISTER_OK);
        return;
    }
    MLOG_W("No key storage found! In not registered...");
}

void AdacuService::KeyStorageSave(const std::vector<uint8_t>& key) {
    StoreUtcToLocal();
    if (StoreKeyStorageToRPMB(key) == 0) {
        MLOG_I("Store key to RPMB success!");
        return;
    }
    MLOG_W("Store key to RPMB failed! Save to local...");

    if (AES_CBC_Encrypt(key, mAesKey, kIV, mKey) != 0) {
        MLOG_E("Key encrypted failed!");
        return;
    }
    if (StoreKeyStorageToLocal(mKey) == 0) {
        MLOG_I("Store key to local success!");
        return;
    }
}

int AdacuService::LoadKeyStorageFromOld() {
    if (!minieye::system::file::IsExist(ADACU_KEY_PATH)) {
        return -1;
    }
    KeyStorage_t storage;
    std::vector<uint8_t> key;

    system::file::ReadFile(ADACU_KEY_PATH, reinterpret_cast<char*>(&storage), sizeof(storage), 0);
    key.assign(std::begin(storage.key), std::end(storage.key));
    mUtc = storage.utc;

    MLOG_D("Got old rawKey: {:Xn}", mlog::toHex({key.begin(), key.end()}));

    if (StoreKeyStorageToRPMB(key) == 0) {
        MLOG_I("Store to RPMB! Remove old file: {} ...", ADACU_KEY_PATH);
        system::file::RemoveFile(ADACU_KEY_PATH);
        return 0;
    }

    MLOG_W("Store key to RPMB failed! Save to local file...");
    if (AES_CBC_Encrypt(key, mAesKey, kIV, mKey) != 0) {
        MLOG_E("Key encrypted failed!");
        return -2;
    }

    if (StoreKeyStorageToLocal(mKey) != 0) {
        MLOG_E("Store key to local failed! Will try next time...");
        return -3;
    }

    system::file::RemoveFile(ADACU_KEY_PATH);

    StoreUtcToLocal();
    return 0;
}

int AdacuService::LoadKeyStorageFromLocal() {
    if (!system::file::IsExist(ADACU_S_KEY_PATH)) {
        return -1;
    }

    int32_t len = 0;
    system::file::ReadFile(ADACU_S_KEY_PATH, reinterpret_cast<char*>(&len), sizeof(int32_t), 0);
    mKey.resize(len);

    system::file::ReadFile(ADACU_S_KEY_PATH, reinterpret_cast<char*>(mKey.data()), len, sizeof(int32_t));

    MLOG_I("Read local key storage success! Try store key to RPMB...");

    std::vector<uint8_t> outKey;
    if (AES_CBC_Decrypt(mKey, mAesKey, kIV, outKey) != 0) {
        MLOG_E("Key decrypted failed!");
        return -2;
    }
    // MLOG_D("OUTKey: {:Xn}", mlog::toHex({outKey.begin(), outKey.end()}));

    if (StoreKeyStorageToRPMB(outKey) == 0) {
        MLOG_I("Store to RPMB! Remove local file: {} ...", ADACU_S_KEY_PATH);
        system::file::RemoveFile(ADACU_S_KEY_PATH);
        return 0;
    }
    MLOG_W("Store key to RPMB failed! Skip steps...");

    LoadUtcFromLocal();
    return 0;
}

int AdacuService::LoadKeyStorageFromRPMB() {
    // 安全存储启用判断
    if (hobot::CryptoKey::LifecycleChk() != 0) {
        MLOG_E("Lifecycle chk failed!");
        return -1;
    }
    std::vector<uint8_t> key;
    if (mAesStorage->Load(ADACU_AES_STORAGE_KEY, key) != 0) {
        MLOG_E("Load key from RPMB failed!");
        return -2;
    }
    MLOG_I("Load key from RPMB success!");

    LoadUtcFromLocal();
    return 0;
}

int AdacuService::LoadUtcFromLocal() {
    if (!system::file::IsExist(ADACU_EXPIRED_UTC_PATH)) {
        MLOG_E("Utc file not found! {}", ADACU_EXPIRED_UTC_PATH);
        return -1;
    }
    system::file::ReadFile(ADACU_EXPIRED_UTC_PATH, reinterpret_cast<char*>(&mUtc), sizeof(mUtc), 0);
    return 0;
}

int AdacuService::StoreKeyStorageToLocal(const std::vector<uint8_t>& key) {
    system::file::RemoveFile(ADACU_S_KEY_PATH);
    system::file::CreateFile(ADACU_S_KEY_PATH);

    int32_t len = key.size();
    system::file::WriteFile(ADACU_S_KEY_PATH, reinterpret_cast<char*>(&len), sizeof(int32_t), 0);
    system::file::WriteFile(ADACU_S_KEY_PATH, reinterpret_cast<const char*>(key.data()), key.size(), sizeof(int32_t));
    system::file::Sync(ADACU_S_KEY_PATH);
    return 0;
}

int AdacuService::StoreKeyStorageToRPMB(const std::vector<uint8_t>& key) {
    // 安全存储启用判断
    if (hobot::CryptoKey::LifecycleChk() != 0) {
        return -1;
    }
    if (mAesStorage->Store(ADACU_AES_STORAGE_KEY, key) != 0) {
        MLOG_E("Store key to RPMB failed!");
        return -2;
    }
    StoreUtcToLocal();
    return 0;
}

int AdacuService::StoreUtcToLocal() {
    system::file::RemoveFile(ADACU_EXPIRED_UTC_PATH);
    system::file::CreateFile(ADACU_EXPIRED_UTC_PATH);
    system::file::WriteFile(ADACU_EXPIRED_UTC_PATH, reinterpret_cast<char*>(&mUtc), sizeof(uint64_t), 0);
    system::file::Sync(ADACU_EXPIRED_UTC_PATH);
    return 0;
}

int AdacuService::InitAesKey() {
    //// >>> 地平线安全存储初始化
    mAesStorage->Reg();

    //// >>> 本地AES初始化
    // AES128
    uint8_t len  = 0;
    int32_t size = 0;
    std::vector<uint8_t> buf(255, 0);

    if (system::file::IsExist(ADACU_AES_KEY_PATH)) {
        size = system::file::ReadFile(ADACU_AES_KEY_PATH, reinterpret_cast<char*>(&len), sizeof(len), 0);
        MLOG_I("AesKey length: {}", len);

        size = system::file::ReadFile(ADACU_AES_KEY_PATH, reinterpret_cast<char*>(buf.data()), len, sizeof(len));
        MLOG_I("Got raw key: {:Xn}", mlog::toHex({buf.begin(), buf.begin() + len}));

        Base64Decode({buf.begin(), buf.begin() + len}, mAesKey);
        MLOG_D("AesKey: {:Xn}", mlog::toHex({mAesKey.begin(), mAesKey.end()}));
        return 0;
    }

    // 随机AES128
    mAesKey.resize(32);
    RAND_bytes(mAesKey.data(), 32);
    MLOG_D("Gened AesKey: {:Xn}", mlog::toHex({mAesKey.begin(), mAesKey.end()}));

    Base64Encode(mAesKey, buf);
    MLOG_I("Got encode key: [{:Xn}] save to file!", mlog::toHex({buf.begin(), buf.end()}));
    len = buf.size();

    system::file::RemoveFile(ADACU_AES_KEY_PATH);
    system::file::CreateFile(ADACU_AES_KEY_PATH);

    system::file::WriteFile(ADACU_AES_KEY_PATH, reinterpret_cast<char*>(&len), sizeof(uint8_t), 0);
    system::file::WriteFile(ADACU_AES_KEY_PATH, reinterpret_cast<char*>(buf.data()), buf.size(), sizeof(uint8_t));
    system::file::Sync(ADACU_AES_KEY_PATH);
    return 0;
}

void AdacuService::GetVidReq(const std::string& host, const VIDReqData_t& req, std::string& rsp) {
    const static std::string url = "/g/40567826/vi/queryVehicleInfoByVin";
    boost::beast::net::io_context ioc;

    // 创建 SSL 流
    boost::beast::ssl_stream<boost::beast::tcp_stream> stream(ioc, kCtx);

    // 设置 SNI
    if (!SSL_set_tlsext_host_name(stream.native_handle(), host.c_str())) {
        throw boost::beast::system_error(::ERR_get_error(), boost::beast::net::error::get_ssl_category());
    }

    // 解析域名
    boost::asio::ip::tcp::resolver resolver(ioc);
    auto const results = resolver.resolve(host, "443");

    // 建立连接
    boost::beast::get_lowest_layer(stream).connect(results);

    // SSL 握手
    stream.handshake(boost::asio::ssl::stream_base::client);

    // 构建请求
    boost::beast::http::request<boost::beast::http::string_body> httpReq{boost::beast::http::verb::get, url, 11};
    httpReq.set(boost::beast::http::field::host, host);
    httpReq.set("vin", req.vin);
    httpReq.set("sn", req.sn);
    httpReq.set("timestamp", req.timestamp);
    httpReq.set("nonce", req.nonce);
    httpReq.set("sign", req.sign);

    // 发送请求
    boost::beast::http::write(stream, httpReq);

    // 接收响应
    boost::beast::flat_buffer buffer;
    boost::beast::http::response<boost::beast::http::dynamic_body> res;
    boost::beast::http::read(stream, buffer, res);

    // 关闭连接
    boost::beast::error_code ec;
    stream.shutdown(ec);

    rsp = boost::beast::buffers_to_string(res.body().data());
}

void AdacuService::VerifyValidWorker() {
    struct timespec now_utc;
    std::vector<char> sn;
    Result_t result;

    VEHICLE_INFO_SERVICE()->GetSN(sn);

    while (mRunning) {
        // 申请中则去get
        if (GetStatus() == E_STATUS_REGISTERING) {
            QueryProgress(result);
            goto SLEEP;
        }

        // 申请过
        if (mUtc != 0) {
            clock_gettime(CLOCK_REALTIME, &now_utc);
            // 大于7天
            if (mUtc - now_utc.tv_sec <= 7 * 24 * 60 * 60) {
                MLOG_I("Key already expired(now: {}, valid: {}), start re-register...", now_utc.tv_sec, mUtc);
                Register(sn);
            }
        }
    SLEEP:
        std::this_thread::sleep_for(std::chrono::seconds(10));
    }
}

void AdacuService::GetVidWorker() {
    VIDReqData_t req;
    std::string rsp, host;
    std::vector<char> tmp;

    // host
    auto env = VEHICLE_INFO_SERVICE()->GetEnv();
    if (env == VehicleInfoService::EnvType_t::E_ENV_TYPE_PP) {
        host = "pp-adcu.roxmotor.com";
    } else if (env == VehicleInfoService::EnvType_t::E_ENV_TYPE_QA) {
        host = "qa-adcu.roxmotor.com";
    } else {
        host = "adcu.roxmotor.com";
    }

    // vin
    VEHICLE_INFO_SERVICE()->GetVIN(tmp);
    req.vin = std::string(tmp.begin(), tmp.end());
    // req.vin = "HJ4ABBHKXPN000246";

    // SN
    VEHICLE_INFO_SERVICE()->GetSN(tmp);
    req.sn = std::string(tmp.begin(), tmp.end());
    // req.sn = "P10287EF0000000310";

    while (mRunning) {
        // ts
        boost::posix_time::ptime epoch(boost::gregorian::date(1970, 1, 1));
        auto diff     = boost::posix_time::microsec_clock::universal_time() - epoch;
        req.timestamp = std::to_string(diff.total_milliseconds());

        // nonce
        char buff[8]{};
        minieye::system::file::ReadFile("/dev/random", buff, sizeof(buff), 0);
        for (int i = 0; i < 8; i++) {
            buff[i] = (buff[i] % 9) + '0';
        }
        req.nonce = std::string(buff);

        // sign
        std::string plainTxt;
        plainTxt += "nonce=";
        plainTxt += req.nonce;
        plainTxt += "&timestamp=";
        plainTxt += req.timestamp;

        std::vector<uint8_t> key, eTxt;
        if (GetKey(key) != 0) {
            goto IDLE;
        }
        // key.assign({0x76, 0xc7, 0xc7, 0x86, 0xe6, 0xee, 0x76, 0x02, 0x9b, 0xc3, 0x0c, 0xf6, 0x83, 0x03, 0xce, 0x61});
        if (AES_CBC_Encrypt({plainTxt.data(), plainTxt.data() + plainTxt.size()}, key, kIV, eTxt) != 0) {
            MLOG_E("AES_CBC_Encrypt failed!");
            goto IDLE;
        }
        
        req.sign = SigCal(eTxt);

        try {
            GetVidReq(host, req, rsp);
        } catch (const std::exception& e) {
            // 网络异常 不再获取
            MLOG_E("Get VID occur net exception: {}", e.what());
            break;
        }

        MLOG_I("Got VID rsp: {}", rsp);
        // 获取成功
        if (VidRspHdler(rsp) == 0) {
            break;
        }
    IDLE:
        MLOG_W("Get VID failed! Try again after 3mins...");
        std::this_thread::sleep_for(std::chrono::minutes(3));
    }
}

int AdacuService::VidRspHdler(const std::string& result) {
    nlohmann::json json;
    try {
        json = nlohmann::json::parse(result);
    } catch (const nlohmann::json::parse_error& e) {
        MLOG_E("JSON parse error: {}", e.what());
        return -1;
    }

    if (json.contains("code") && json["code"] == 0 && json.contains("data") && json["data"].contains("openId")) {
        mVid = json["data"]["openId"];
        MLOG_I("Got VID: {}", mVid);

        // system::file::RemoveFile(ADACU_VID_PATH);
        // system::file::CreateFile(ADACU_VID_PATH);

        // system::file::WriteFile(ADACU_VID_PATH, vid.data(), vid.size(), 0);
        return 0;
    }
    return -2;
}

void AdacuService::OnProcQuery(SysInfoType type, std::vector<char>& data) {
    if (type != E_SYS_INFO_TYPE_ADACU && type != E_SYS_INFO_TYPE_VID) {
        return;
    }
    MLOG_I("OnProcQuery: send ack...!");

    if (type == E_SYS_INFO_TYPE_VID) {
        MLOG_I("AdacuService::OnProcQuery: E_SYS_INFO_TYPE_VID");
        if (mVid.empty()) {
            return;
        }
        data.assign(mVid.begin(), mVid.end());
        return;
    }

    if (type == E_SYS_INFO_TYPE_ADACU) {
        MLOG_I("AdacuService::OnProcQuery: E_SYS_INFO_TYPE_ADACU");
        std::vector<uint8_t> key;
        if (GetKey(key) != 0) {
            return;
        }
        data.resize(key.size() + sizeof(mUtc));
        memcpy(data.data(), key.data(), key.size());
        memcpy(data.data() + key.size(), &mUtc, sizeof(mUtc));
    }
}