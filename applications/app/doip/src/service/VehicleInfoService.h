/*
 * Copyright [2025] MINIEYE
 * Descripttion : 车辆信息服务
 * Author       : zhouyuxuan
 * Date         : 2025-06-03
 */

#pragma once

#include "SysInfoType.h"
#include "Service.hpp"

namespace minieye {
namespace dolink {

class VehicleInfoService : public Service {
    PROPERTY(E_SERVICE_VEHICLE_INFO, VehicleInfoService)

    enum EnvType_t {
        E_ENV_TYPE_UNKNOWN = 0,
        E_ENV_TYPE_P,
        E_ENV_TYPE_PP,
        E_ENV_TYPE_QA,
        E_ENV_TYPE_MAX
    };

 public:
    VehicleInfoService();
    ~VehicleInfoService();

    bool Start() override;

    void GetSN(std::vector<char>& data);
    bool SetSN(const std::vector<char>& data);

    void GetVIN(std::vector<char>& data);
    bool SetVIN(const std::vector<char>& data);

    void GetFingerprint(std::vector<char>& data);
    bool SetFingerprint(const std::vector<char>& data);

    void GetEquipmentMatintenanceFingerprint(std::vector<char>& data);
    bool SetEquipmentMatintenanceFingerprint(const std::vector<char>& data);

    void GetVehicleConfiguration(std::vector<char>& data);
    bool SetVehicleConfiguration(const std::vector<char>& data);

    void GetECUHardwareVersion(std::vector<char>& data);

    void GetECUSoftwareVersion(std::vector<char>& data);

    void GetECUManufactureDate(std::vector<char>& data);
    bool SetECUManufactureDate(const std::vector<char>& data);

    inline EnvType_t GetEnv() const { return mEnv; }
    void GetEnv(std::vector<char>& data);
    void SetEnv(EnvType_t env);

 private:
    void SetVersion2MCU();

    void GetQCraftVersion();
    
    void LoadEnv();
    void StoreEnv();
    void GetEnvSettings(std::vector<char>& data);

    void OnProcQuery(SysInfoType type, std::vector<char>& data);

 private:
    const std::vector<char> mEcuVerHw;
    const std::vector<char> mEcuVerSw;

    EnvType_t               mEnv;
};

}  // namespace dolink
}  // namespace minieye
