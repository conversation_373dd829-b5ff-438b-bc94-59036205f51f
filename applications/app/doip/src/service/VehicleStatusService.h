/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : zhouyuxuan
 * Date         : 2025-04-29
 */

#pragma once

#include <atomic>

#include <IPC_matrix_Middleware.h>

#include "Service.hpp"
#include "CanService.h"

namespace minieye {
namespace dolink {

class VehicleStatusService : public Service {
    PROPERTY(E_SERVICE_VEHICLE_STATUS, VehicleStatusService)

 public:
    enum EPBStatus_t : uint8_t {
        E_EPB_STATUS_APPLIED             = 0x00,
        E_EPB_STATUS_RELEASED            = 0x01,
        E_EPB_STATUS_APPLYING            = 0x02,
        E_EPB_STATUS_RELEASING           = 0x03,
        E_EPB_STATUS_UNKNOWN             = 0x04,
        E_EPB_STATUS_HOLD_APPLIED        = 0x05,
        E_EPB_STATUS_COMPLETELY_RELEASED = 0x06,
        E_EPB_STATUS_HAP_PREPARED        = 0x07,
    };
    enum GearPos_t : uint8_t {
        E_GEAR_POS_INITIAL_STATE         = 0x00,
        E_GEAR_POS_R                     = 0x01,
        E_GEAR_POS_N                     = 0x02,
        E_GEAR_POS_D                     = 0x03,
        E_GEAR_POS_P                     = 0x04,
        E_GEAR_POS_RESERVED_1            = 0x05,
        E_GEAR_POS_RESERVED_2            = 0x06,
        E_GEAR_POS_INVALID               = 0x07,
    };
    enum CdsHeightLvl_t : uint8_t {
        E_CDS_HEIGHT_LVL_UNKNOWN         = 0,
        E_CDS_HEIGHT_LVL_HIGHEST         = 0x01,  // 最高
        E_CDS_HEIGHT_LVL_HIGHER          = 0x02,  // 较高
        E_CDS_HEIGHT_LVL_STANDARD        = 0x03,  // 标准
        E_CDS_HEIGHT_LVL_LOWER           = 0x04,  // 较低
        E_CDS_HEIGHT_LVL_LOWEST          = 0x05,  // 最低
        E_CDS_HEIGHT_LVL_OUT_OF_TROUBLE  = 0x06,  // 脱困
    };
    struct AbsTime {
        uint8_t year;
        uint8_t month;
        uint8_t day;
        uint8_t hour;
        uint8_t min;
        uint8_t sec;
    };

 public:
    VehicleStatusService();
    ~VehicleStatusService();

    bool ConditionChk();

    bool CalibConditionCheck();

    inline AbsTime GetAbsTime() {
        return mTime;
    }
    inline int32_t GetOdometer() {
        return mOdometer;
    }
    inline double GetBatteryVoltage() {
        return mBatteryVol;
    }
    inline double GetVehicleSpeed() {
        return mSpeed;
    }
    inline EPBStatus_t GetEPBPrimary() {
        return static_cast<EPBStatus_t>(mEPBPrimary.load());
    }
    inline EPBStatus_t GetEPBSecondary() {
        return static_cast<EPBStatus_t>(mEPBSecondary.load());
    }
    inline GearPos_t GetGearPos() {
        return static_cast<GearPos_t>(mGearpos.load());
    }
    inline bool GetDoorOpened() {
        return mDoorOpened;
    }
    inline bool GetSideMirrFolded() {
        return mMirrorFolded;
    }
    inline CdsHeightLvl_t GetCdsHeightLvl() {
        return static_cast<CdsHeightLvl_t>(mCdsHeightLvl.load());
    }
    inline bool GetLightOpened() {
        return mLightOpened;
    }
    inline bool GetCameraError() {
        return mCameraError;
    }

 private:
    void CanData_0x10000727_Hdler(const char* data, uint8_t len);

    void CanData_SnapShot_Hdler(can_obj_ipc_matrix_middleware_h_t& obj);
    void CanData_CameraSts_Hdler(can_obj_ipc_matrix_middleware_h_t& obj);
    void CanData_VehicleSts_Hdler(can_obj_ipc_matrix_middleware_h_t& obj);

    void ProcMsg(canid_t canid, const char* data, uint8_t len);

 private:
    std::atomic_uint8_t               mEPBPrimary{};
    std::atomic_uint8_t               mEPBSecondary{};
    std::atomic_uint8_t               mGearpos{};

    std::atomic<double>               mBatteryVol{};
    std::atomic<double>               mSpeed{};
    std::atomic<AbsTime>              mTime{};
    std::atomic_int32_t               mOdometer{};

    std::atomic_bool                  mDoorOpened{false};    // 车门未全部关闭
    std::atomic_bool                  mMirrorFolded{false};  // 任意后视镜折叠
    std::atomic_bool                  mLightOpened{false};   // 灯光未全部关闭
    std::atomic_uint8_t               mCdsHeightLvl{};
    
    std::atomic_bool                  mCameraError{false};  //
};

}  // namespace dolink
}  // namespace minieye
