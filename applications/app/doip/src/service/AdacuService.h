/*
 * Copyright [2025] MINIEYE
 * Descripttion : ADACU密钥服务
 * Author       : zhouyuxuan
 * Date         : 2025-05-30
 */

#pragma once

#include <atomic>
#include <memory>

#include <boost/beast/core.hpp>
#include <boost/beast/http.hpp>
#include <boost/beast/ssl.hpp>
#include <boost/beast/version.hpp>

#include "SysInfoType.h"
#include "VehicleInfoService.h"
#include "Service.hpp"
#include "CryptoKey.h"

namespace minieye {
namespace dolink {

class AdacuService : public Service {
    PROPERTY(E_SERVICE_ADACU, AdacuService)
    struct VIDReqData_t {
        std::string vin;
        std::string sn;
        std::string timestamp;
        std::string nonce;
        std::string sign;
    };

 public:
    enum Status_t {
        E_STATUS_UNKNOWN = 0,       // 未知状态
        E_STATUS_REGISTER_BAD,      // 申请失败
        E_STATUS_REGISTER_OK,       // 申请成功
        E_STATUS_REGISTER_EXPIRED,  // 过期
        E_STATUS_NOT_REGISTER,      // 未申请 (内部)
        E_STATUS_REGISTERING,       // 申请中 (内部)
    };
    enum Result_t : uint8_t {
        E_RESULT_NOT_REGISTER = 0,         // ADCU未申请通信密钥
        E_RESULT_REGISTERING,              // ADCU密钥申请执行中
        E_RESULT_TBOX_TIMEOUT,             // TBOX回复超时(30s)
        E_RESULT_TBOX_NO_NET,              // TBOX无网络
        E_RESULT_TSP_CONN_FAIL,            // TSP connection failure
        E_RESULT_ECU_INFO_EMPTY,           // ECU info(vin/sn and so on)空
        E_RESULT_TBOX_CERT_DOWNLOAD_FAIL,  // TBOX证书下载失败
        E_RESULT_TBOX_CERT_VERIFY_FAIL,    // TBOX证书校验错误
        E_RESULT_TBOX_GET_KEY_FAIL,        // TBOX通信密钥获取失败
        E_RESULT_TBOX_GET_KEY_OK,          // ADCU获取通信密钥成功
    };

 public:
    AdacuService();
    ~AdacuService();

    bool Start() override;
    void Stop() override;

    bool Register(const std::vector<char>& sn);
    bool QueryProgress(Result_t& result);

    uint64_t GetUtc() const {
        return mUtc;
    }    

    inline Status_t GetStatus() {
        return mSts;
    }

 private:
    int GetKey(std::vector<uint8_t>& key);

    inline void SetStatus(Status_t sts) {
        mSts = sts;
    }

    void KeyStorageRead();
    void KeyStorageSave(const std::vector<uint8_t>& key);

    int LoadKeyStorageFromOld();
    int LoadKeyStorageFromLocal();
    int LoadKeyStorageFromRPMB();
    int LoadUtcFromLocal();

    int StoreKeyStorageToLocal(const std::vector<uint8_t>& key);
    int StoreKeyStorageToRPMB(const std::vector<uint8_t>& key);
    int StoreUtcToLocal();

    int InitAesKey();

    void GetVidReq(const std::string& host, const VIDReqData_t& req, std::string& rsp);
    int VidRspHdler(const std::string& result);

    void VerifyValidWorker();
    void GetVidWorker();
    
    void OnProcQuery(SysInfoType type, std::vector<char>& data);


 private:
    Status_t                        mSts{E_STATUS_NOT_REGISTER};
    std::unique_ptr<hobot::AESKey>  mAesStorage;
    std::vector<uint8_t>            mAesKey;
    std::vector<uint8_t>            mKey;
    std::string                     mVid;
    uint64_t                        mUtc{0};

    std::atomic_bool                mRunning;
    std::thread                     mVerifyWorker;
    std::thread                     mGetVidWorker;
};

}  // namespace dolink
}  // namespace minieye
