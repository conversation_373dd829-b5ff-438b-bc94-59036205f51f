#include "CalibService.h"
#include <string>
#include <memory>
#include <utility>
using namespace minieye::dolink;

/**
 * description: 标定总的结果错误码
 */
enum CalibErrCode {
    E_CALIB_ERR_CODE_CAMERA_SUCCESS = 0x21,    // 摄像头标定成功
    E_CALIB_ERR_CODE_CAMERA_RUN_ERROR = 0x27,  // 摄像头标定运行异常
};

CalibService::CalibService()
    : Service("Calib")
    , mExit(false)
    , mCalibrateComplete(false)
    , mSteadyTimer(mIoContext)
    , mFaultCount(0)
    , mIsUpdate(false)
    , mCalibTimeout(false) {
}

CalibService::~CalibService() = default;

bool CalibService::Start() {
    mCalibServerPtr = minieye::calib::ICalibServer::GetInstance();
    if (mCalibServerPtr->Init() != 0) {
        MLOG_E("calib service com init fail");
        return false;
    }
    mCalibServerPtr->RegisterResCallback(std::bind(&CalibService::OnSurveyRes, this, std::placeholders::_1));
    // 创建work_guard，保持io_context运行
    mWorkGuard = std::make_unique<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>>(
        mIoContext.get_executor());
    mIoThread = std::thread([this]() {
        mIoContext.run();
        MLOG_I("IO context run end");
    });
    MLOG_I("CalibService::Start end");
    return true;
}

int CalibService::TriggerDrivingCameraCalib(std::vector<char>& result) {
    MLOG_I("TriggerDrivingCameraCalib");
    // TODO(yicheng) 启动行车摄像头标定流程
    // std::vector<int> cameras;
    mFaultCount = 0;
    result.resize(8, minieye::calib::E_FACTORY_CAMERA_CALIBRATION_DEFAULT);  // 初始化结果为默认值
    result[0] = E_CALIB_ERR_CODE_CAMERA_SUCCESS;                             // 默认标定成功
    std::unique_lock<std::mutex> lock(mCalibCompleteMutex);
    mDeviceVec.clear();
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_FRONT_LONG_VIEW_CAMERA, false));  // 前视
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_REAR_CENTER_CAMERA, false));      // 后视
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_RIGHT_REAR_WING_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_FRONT_WIDE_VIEW_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_LEFT_REAR_WING_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_LEFT_FRONT_WING_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_RIGHT_FRONT_WING_CAMERA, false));
    memset(&mRequestMsg, 0, sizeof(mRequestMsg));
    memset(&mResponseMsg, 0, sizeof(mResponseMsg));
    mRequestMsg.calibType = minieye::calib::E_CALIB_TYPE_FRONT_SIDE_REAR_CAMERA;
    mRequestMsg.workStation = minieye::calib::E_CALIB_WORKSTATION_1;
    mRequestMsg.calibNum = mDeviceVec.size();

    // 请求启动标定
    int res = RequestCalib(minieye::calib::E_CALIB_CMD_START);
    if (res != 0) {
        MLOG_E("start driving camera calibration failed");
        for (int i = 0; i < result.size(); i++) {
            if (i == 0) {
                result[0] = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;  // 标定总结果为失败
            } else {
                result[i] = minieye::calib::E_FACTORY_CAMERA_CALIBRATION_DEFAULT;
            }
        }
        return -1;
    }

    StartCalibrationTimer();  // 启动周期查询结果任务

    mCalibrateComplete = false;
    mCalibTimeout = false;
    if (mCalibCompleteNotify.wait_for(lock, std::chrono::seconds(120), [this]() { return mCalibrateComplete; })) {
        // TODO(yicheng) 标定成功或失败
        if (mCalibrateComplete) {
            MLOG_I("TriggerDrivingCameraCalib calibration completed");
        } else {
            MLOG_E("TriggerDrivingCameraCalib calibration not complete");
        }
    } else {
        mCalibTimeout = true;
        MLOG_E("TriggerDrivingCameraCalib calibration time out");
        result[0] = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
        for (int i = 0; i < minieye::calib::E_DEVICE_ID_MAX; i++) {
            if (mResponseMsg.deviceMsg[i].errCode == minieye::calib::E_FACTORY_CAMERA_CALIBRATION_RUNNING ||
                mResponseMsg.deviceMsg[i].errCode == minieye::calib::E_FACTORY_CAMERA_CALIBRATION_DEFAULT) {
                mResponseMsg.deviceMsg[i].errCode = minieye::calib::E_FACTORY_CAMERA_CALIBRATION_OVER_TIME_LIMIT;
            }
        }
        // TODO(yicheng) 停止标定任务 @2025-08-22 下个版本打开
        if (RequestCalib(minieye::calib::E_CALIB_CMD_STOP) != 0) {
            MLOG_E("stop TriggerDrivingCameraCalib calibration failed");
        }
    }
    // TODO(yicheng) 填充标定结果
    result[0] = E_CALIB_ERR_CODE_CAMERA_SUCCESS;
    result[1] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_FRONT_LONG_VIEW_CAMERA].errCode;
    result[2] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_REAR_CENTER_CAMERA].errCode;
    result[3] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_LEFT_FRONT_WING_CAMERA].errCode;
    result[4] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_RIGHT_FRONT_WING_CAMERA].errCode;
    result[5] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_FRONT_WIDE_VIEW_CAMERA].errCode;
    result[6] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_LEFT_REAR_WING_CAMERA].errCode;
    result[7] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_RIGHT_REAR_WING_CAMERA].errCode;
    for (int i = 0; i < mDeviceVec.size(); i++) {
        if (mResponseMsg.deviceMsg[mDeviceVec[i].first].calibState == minieye::calib::E_CALIB_STATE_FAILURE ||
            mResponseMsg.deviceMsg[mDeviceVec[i].first].errCode !=
                minieye::calib::E_FACTORY_CAMERA_CALIBRATION_SUCCEED) {
            result[0] = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
        }
    }
    MLOG_I("TriggerDrivingCameraCalib end");
    return 0;
}
void CalibService::GetCalibResult(std::vector<char>& result) {
}
int CalibService::TriggerAvmCameraCalib(std::vector<char>& result) {
    MLOG_I("TriggerAvmCameraCalib");
    // TODO(yicheng) 启动泊车环视摄像头标定流程
    mFaultCount = 0;
    std::unique_lock<std::mutex> lock(mCalibCompleteMutex);
    result.resize(5, minieye::calib::E_FACTORY_CAMERA_CALIBRATION_DEFAULT);  // 初始化结果为默认值
    result[0] = E_CALIB_ERR_CODE_CAMERA_SUCCESS;                             // 默认标定成功
    // result[0] = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
    mDeviceVec.clear();
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_FRONT_CENTER_AROUND_VIEW_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_REAR_CENTER_AROUND_VIEW_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_LEFT_SIDE_AROUND_VIEW_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_RIGHT_SIDE_AROUND_VIEW_CAMERA, false));
    memset(&mRequestMsg, 0, sizeof(mRequestMsg));
    memset(&mResponseMsg, 0, sizeof(mResponseMsg));
    mRequestMsg.calibType = minieye::calib::E_CALIB_TYPE_FISHEYE_CAMERA;
    mRequestMsg.workStation = minieye::calib::E_CALIB_WORKSTATION_1;
    mRequestMsg.calibNum = mDeviceVec.size();
    // for (int i = 0; i < mDeviceVec.size(); i++) {
    //     mRequestMsg.deviceMsg[mDeviceVec[i].first].cmd = minieye::calib::E_CALIB_CMD_START;
    //     mRequestMsg.deviceMsg[mDeviceVec[i].first].deviceId = mDeviceVec[i].first;
    // }

    // // 清空回复状态
    // for (int i = 0; i < mDeviceVec.size(); i++) {
    //     mDeviceVec[i].second = false;
    // }
    // if (mCalibServerPtr->Reqeust(mRequestMsg) != 0) {
    //     MLOG_E("TriggerAvmCameraCalib request driving camera calib start fail");
    // }
    // 请求启动标定
    int res = RequestCalib(minieye::calib::E_CALIB_CMD_START);
    if (res != 0) {
        MLOG_E("start TriggerAvmCameraCalib calibration failed");
        // for (auto& i : result) {
        for (int i = 0; i < result.size(); i++) {
            if (i == 0) {
                result[0] = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
            } else {
                result[i] = minieye::calib::E_FACTORY_CAMERA_CALIBRATION_DEFAULT;
            }
        }
        return -1;
    }
    StartCalibrationTimer();  // 启动周期查询结果任务

    mCalibrateComplete = false;
    mCalibTimeout = false;
    bool ret = mCalibCompleteNotify.wait_for(lock, std::chrono::seconds(120), [this]() { return mCalibrateComplete; });
    if (ret) {
        // TODO(yicheng) 标定成功或失败
        if (mCalibrateComplete) {
            MLOG_I("TriggerAvmCameraCalib calibration completed");
        } else {
            MLOG_E("TriggerDrivingCameraCalib calibration not complete");
        }
    } else {
        mCalibTimeout = true;
        MLOG_E("TriggerAvmCameraCalib calibration time out");
        result[0] = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
        for (int i = 0; i < minieye::calib::E_DEVICE_ID_MAX; i++) {
            if (mResponseMsg.deviceMsg[i].errCode == minieye::calib::E_FACTORY_CAMERA_CALIBRATION_RUNNING ||
                mResponseMsg.deviceMsg[i].errCode == minieye::calib::E_FACTORY_CAMERA_CALIBRATION_DEFAULT) {
                mResponseMsg.deviceMsg[i].errCode = minieye::calib::E_FACTORY_CAMERA_CALIBRATION_OVER_TIME_LIMIT;
            }
        }
        // TODO(yicheng) 停止标定任务 @2025-08-22 下个版本打开
        if (RequestCalib(minieye::calib::E_CALIB_CMD_STOP) != 0) {
            MLOG_E("stop TriggerAvmCameraCalib calibration failed");
        }
    }
    // TODO(yicheng) 填充标定结果
    result[0] = E_CALIB_ERR_CODE_CAMERA_SUCCESS;
    result[1] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_FRONT_CENTER_AROUND_VIEW_CAMERA].errCode;
    result[2] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_REAR_CENTER_AROUND_VIEW_CAMERA].errCode;
    result[3] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_LEFT_SIDE_AROUND_VIEW_CAMERA].errCode;
    result[4] = mResponseMsg.deviceMsg[minieye::calib::E_DEVICE_ID_RIGHT_SIDE_AROUND_VIEW_CAMERA].errCode;
    for (int i = 0; i < mDeviceVec.size(); i++) {
        if (mResponseMsg.deviceMsg[mDeviceVec[i].first].calibState == minieye::calib::E_CALIB_STATE_FAILURE ||
            mResponseMsg.deviceMsg[mDeviceVec[i].first].errCode !=
                minieye::calib::E_FACTORY_CAMERA_CALIBRATION_SUCCEED) {
            result[0] = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
        }
    }
    MLOG_I("TriggerAvmCameraCalib end");
    return 0;
}
int CalibService::TriggerDrivingCameraAfterSaleCalib() {
    MLOG_I("TriggerDrivingCameraAfterSaleCalib");
    // TODO(yicheng) 启动行车摄像头标定流程
    // std::vector<int> cameras;
    // result.resize(8, E_CALIB_ERR_CODE_CAMERA_RUN_ERROR);  // 初始化结果为失败
    // result[0] = E_CALIB_ERR_CODE_CAMERA_SUCCESS;
    std::unique_lock<std::mutex> lock(mCalibCompleteMutex);
    mDeviceVec.clear();
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_FRONT_LONG_VIEW_CAMERA, false));  // 前视
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_REAR_CENTER_CAMERA, false));      // 后视
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_RIGHT_REAR_WING_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_FRONT_WIDE_VIEW_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_LEFT_REAR_WING_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_LEFT_FRONT_WING_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_RIGHT_FRONT_WING_CAMERA, false));
    memset(&mRequestMsg, 0, sizeof(mRequestMsg));
    memset(&mResponseMsg, 0, sizeof(mResponseMsg));
    mRequestMsg.calibType = minieye::calib::E_CALIB_TYPE_FRONT_SIDE_REAR_CAMERA_EXT_4S;
    mRequestMsg.workStation = minieye::calib::E_CALIB_WORKSTATION_1;
    mRequestMsg.calibNum = mDeviceVec.size();

    // 请求启动标定
    int res = RequestCalibNoRsp(minieye::calib::E_CALIB_CMD_START);
    if (res != 0) {
        MLOG_E("start driving camera after sale calibration failed");
        return -1;
    }
    return 0;
}

int CalibService::TriggerAvmCameraAfterSaleCalib() {
    MLOG_I("TriggerAvmCameraAfterSaleCalib");
    // TODO(yicheng) 启动泊车环视摄像头标定流程
    std::unique_lock<std::mutex> lock(mCalibCompleteMutex);
    // result.resize(5, E_CALIB_ERR_CODE_CAMERA_RUN_ERROR);  // 初始化结果为失败
    // result[0] = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
    mDeviceVec.clear();
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_FRONT_CENTER_AROUND_VIEW_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_REAR_CENTER_AROUND_VIEW_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_LEFT_SIDE_AROUND_VIEW_CAMERA, false));
    mDeviceVec.push_back(std::make_pair(minieye::calib::E_DEVICE_ID_RIGHT_SIDE_AROUND_VIEW_CAMERA, false));
    memset(&mRequestMsg, 0, sizeof(mRequestMsg));
    memset(&mResponseMsg, 0, sizeof(mResponseMsg));
    mRequestMsg.calibType = minieye::calib::E_CALIB_TYPE_FISHEYE_CAMERA_EXT_4S;
    mRequestMsg.workStation = minieye::calib::E_CALIB_WORKSTATION_1;
    mRequestMsg.calibNum = mDeviceVec.size();

    // 请求启动标定
    int res = RequestCalibNoRsp(minieye::calib::E_CALIB_CMD_START);
    if (res != 0) {
        MLOG_E("start avm camera after sale calibration failed");
        return -1;
    }
    return 0;
}

int CalibService::GetDrivingCameraAfterSaleCalibResult(std::vector<char>& result) {
    MLOG_I("GetDrivingCameraAfterSaleCalibResult");

    if (RequestCalib(minieye::calib::E_CALIB_CMD_RESULTS) != 0) {
        MLOG_E("GetDrivingCameraAfterSaleCalibResult request result failed");
    } else {
        MLOG_I("GetDrivingCameraAfterSaleCalibResult request result success");
    }

    result.resize(mDeviceVec.size(), minieye::calib::E_AFTERSALES_CAMERA_CALIBRATION_DEFAULT);

    CalibErrCode res = E_CALIB_ERR_CODE_CAMERA_SUCCESS;
    std::unique_lock<std::mutex> lock(mResMutex);

    for (auto& i : mDeviceVec) {
        if (i.second) {
            continue;
        }
        auto cam = mResponseMsg.deviceMsg[i.first];
        if (cam.calibState == minieye::calib::E_CALIB_STATE_FAILURE) {
            res = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
        }

        switch (i.first) {
#define XX(INDEX, CAM_TYPE)          \
    case minieye::calib::CAM_TYPE:   \
        result[INDEX] = cam.errCode; \
        break;

            XX(0, E_DEVICE_ID_FRONT_LONG_VIEW_CAMERA)
            XX(1, E_DEVICE_ID_REAR_CENTER_CAMERA)
            XX(2, E_DEVICE_ID_LEFT_FRONT_WING_CAMERA)
            XX(3, E_DEVICE_ID_RIGHT_FRONT_WING_CAMERA)
            XX(4, E_DEVICE_ID_FRONT_WIDE_VIEW_CAMERA)
            XX(5, E_DEVICE_ID_LEFT_REAR_WING_CAMERA)
            XX(6, E_DEVICE_ID_RIGHT_REAR_WING_CAMERA)
#undef XX
        }
    }
    result.push_back(static_cast<char>(mResponseMsg.process));
    return res;
}

int CalibService::GetAvmCameraAfterSaleCalibResult(std::vector<char>& result) {
    MLOG_I("GetAvmCameraAfterSaleCalibResult");

    if (RequestCalib(minieye::calib::E_CALIB_CMD_RESULTS) != 0) {
        MLOG_E("GetAvmCameraAfterSaleCalibResult request result failed");
    } else {
        MLOG_I("GetAvmCameraAfterSaleCalibResult request result success");
    }

    result.resize(mDeviceVec.size(), minieye::calib::E_AFTERSALES_CAMERA_CALIBRATION_DEFAULT);

    CalibErrCode res = E_CALIB_ERR_CODE_CAMERA_SUCCESS;
    std::unique_lock<std::mutex> lock(mResMutex);

    for (auto& i : mDeviceVec) {
        if (i.second) {
            continue;
        }
        auto cam = mResponseMsg.deviceMsg[i.first];
        if (cam.calibState == minieye::calib::E_CALIB_STATE_FAILURE) {
            res = E_CALIB_ERR_CODE_CAMERA_RUN_ERROR;
        }

        switch (i.first) {
#define XX(INDEX, CAM_TYPE)          \
    case minieye::calib::CAM_TYPE:   \
        result[INDEX] = cam.errCode; \
        break;

            XX(0, E_DEVICE_ID_FRONT_CENTER_AROUND_VIEW_CAMERA)
            XX(1, E_DEVICE_ID_REAR_CENTER_AROUND_VIEW_CAMERA)
            XX(2, E_DEVICE_ID_LEFT_SIDE_AROUND_VIEW_CAMERA)
            XX(3, E_DEVICE_ID_RIGHT_SIDE_AROUND_VIEW_CAMERA)
#undef XX
        }
    }
    result.push_back(static_cast<char>(mResponseMsg.process));
    return res;
}

void CalibService::StartCalibrationTimer() {
    MLOG_I("StartCalibrationTimer");
    std::chrono::milliseconds cyclic_calib_delay_ = std::chrono::milliseconds(1100);
    boost::system::error_code ec;
    mSteadyTimer.expires_from_now(cyclic_calib_delay_, ec);
    mSteadyTimer.async_wait(std::bind(&CalibService::OnCalibrationTimerExpired, this, std::placeholders::_1));
}

void CalibService::OnCalibrationTimerExpired(const boost::system::error_code& _error) {
    MLOG_I("OnCalibrationTimerExpired mDeviceVec.size():{}", mDeviceVec.size());
    if (_error) {
        MLOG_E("OnCalibrationTimerExpired error : {}", _error.message());
        return;
    }
    if (mCalibTimeout) {
        MLOG_E("OnCalibrationTimerExpired timeout ,stop task");
        return;
    }
    // TODO(yicheng) 发送请求结果
    if (RequestCalib(minieye::calib::E_CALIB_CMD_RESULTS) != 0) {
        // 某个camera请求超时，继续请求，连续3次出现超时，判定标定失败
        MLOG_E("OnCalibrationTimerExpired request result timeout ");
        mFaultCount++;
        if (mFaultCount > 3) {
            // TODO(yicheng) 标定成功或者失败
            std::unique_lock<std::mutex> lock(mCalibCompleteMutex);
            mCalibrateComplete = true;
            mFaultCount = 0;
            mCalibCompleteNotify.notify_all();
            MLOG_I("calibration failed");
            return;
        }
        if (mCalibTimeout) {
            MLOG_E("OnCalibrationTimerExpired timeout ,stop task");
            return;
        }
        StartCalibrationTimer();
        MLOG_I("not complete, continue request calibration result");
        return;
    }
    // TODO(yicheng) 判断标定结果
    bool isAllComplete = false;
    bool isHasRunning = false;
    for (auto id : mDeviceVec) {
        if (mResponseMsg.deviceMsg[id.first].calibState == minieye::calib::E_CALIB_STATE_CALIB_RUNNING) {
            isHasRunning = true;
        } else if (mResponseMsg.deviceMsg[id.first].calibState == minieye::calib::E_CALIB_STATE_FAILURE) {
            isAllComplete = true;
            break;
        }
    }
    if (isHasRunning && !isAllComplete) {
        StartCalibrationTimer();
        MLOG_I("not complete, continue request calibration result");
    } else {
        // TODO(yicheng) 标定成功或者失败
        std::unique_lock<std::mutex> lock(mCalibCompleteMutex);
        mCalibrateComplete = true;
        mCalibCompleteNotify.notify_all();
        MLOG_I("OnCalibrationTimerExpired all calibration complete");
    }
}
int CalibService::RequestCalib(int cmd) {
    // 清空回复状态
    for (int i = 0; i < mDeviceVec.size(); i++) {
        mDeviceVec[i].second = false;
    }

    for (int i = 0; i < mDeviceVec.size(); i++) {
        mRequestMsg.deviceMsg[mDeviceVec[i].first].cmd = cmd;
        mRequestMsg.deviceMsg[mDeviceVec[i].first].deviceId = mDeviceVec[i].first;
    }
    // DumpInfo(mRequestMsg);
    // 发送标定请求
    if (mCalibServerPtr->Reqeust(mRequestMsg) != 0) {
        MLOG_E("CalibService::RequestCalib request failed");
        return -1;
    }
    std::unique_lock<std::mutex> lock(mResMutex);

    if (!mResNotify.wait_for(lock, std::chrono::milliseconds(1000), [this]() { return this->mIsUpdate; })) {
        // 请求响应超时
        MLOG_E("response :{} timeout 1000ms",
               ((cmd == minieye::calib::E_CALIB_CMD_START) ? ("request start") : ("request result")));
        // 设置对应响应超时id错误码
        for (auto& i : mDeviceVec) {
            if (!i.second) {
                mResponseMsg.deviceMsg[i.first].calibState = minieye::calib::E_CALIB_STATE_FAILURE;
                if (mRequestMsg.calibType == minieye::calib::E_CALIB_TYPE_FRONT_SIDE_REAR_CAMERA ||
                    mRequestMsg.calibType == minieye::calib::E_CALIB_TYPE_FISHEYE_CAMERA) {  // EOL标定
                    mResponseMsg.deviceMsg[i.first].errCode =
                        minieye::calib::E_FACTORY_CAMERA_CALIBRATION_OVER_TIME_LIMIT;
                } else {  // 4S售后标定
                    mResponseMsg.deviceMsg[i.first].errCode =
                        minieye::calib::E_AFTERSALES_CAMERA_CALIBRATION_OVER_TIME_LIMIT;
                }

                MLOG_E("camera id:{} timeout , calibStats:{} , errCode:{}",
                       i.first,
                       mResponseMsg.deviceMsg[i.first].calibState,
                       mResponseMsg.deviceMsg[i.first].errCode);
            }
        }
        return -1;
    } else {
        // 所有请求已回复
    }
    return 0;
}

int CalibService::RequestCalibNoRsp(int cmd) {
    // 清空回复状态
    for (int i = 0; i < mDeviceVec.size(); i++) {
        mDeviceVec[i].second = false;
    }

    for (int i = 0; i < mDeviceVec.size(); i++) {
        mRequestMsg.deviceMsg[mDeviceVec[i].first].cmd = cmd;
        mRequestMsg.deviceMsg[mDeviceVec[i].first].deviceId = mDeviceVec[i].first;
    }
    // DumpInfo(mRequestMsg);
    // 发送标定请求
    if (mCalibServerPtr->Reqeust(mRequestMsg) != 0) {
        MLOG_E("CalibService::RequestCalib request failed");
        return -1;
    }
    return 0;
}

void CalibService::OnSurveyRes(minieye::calib::CalibResponseMsg resMsg) {
    MLOG_I("CalibService::OnSurveyRes");
    mResponseMsg.process = resMsg.process;
    for (int i = 0; i < minieye::calib::E_DEVICE_ID_MAX; i++) {
        if (resMsg.deviceMsg[i].deviceId != 0) {
            memcpy(&mResponseMsg.deviceMsg[i], &resMsg.deviceMsg[i], sizeof(minieye::calib::DeviceResMsg));
            SetCurResStatus(resMsg.deviceMsg[i].deviceId, true);  // 设置当前id回复状态为true
            // mRequestMsg.deviceMsg[i].calibState = resMsg.deviceMsg[i].calibState;
            std::string res;
            if (mResponseMsg.deviceMsg[i].calibState == minieye::calib::E_CALIB_STATE_CALIB_RUNNING) {
                res = std::string("calib running");
            } else if (mResponseMsg.deviceMsg[i].calibState == minieye::calib::E_CALIB_STATE_FAILURE) {
                res = std::string("calib failed errCode") + std::to_string(mResponseMsg.deviceMsg[i].errCode);
            } else if (mResponseMsg.deviceMsg[i].calibState == minieye::calib::E_CALIB_STATE_SUCCESS) {
                res = std::string("calib success");
            } else {
                res = std::string("calib unkown state:") + std::to_string(mResponseMsg.deviceMsg[i].calibState);
            }
            // std::cout << " server recv device id:(" << resMsg.deviceMsg[i].deviceId << ") " + res << std::endl;
            MLOG_I("calib server recv device id:({}) {}", mResponseMsg.deviceMsg[i].deviceId, res);
        }
    }
    if (CheckRespResult()) {
        // 当前请求所有id都应答，通知request
        mIsUpdate = true;
        mResNotify.notify_all();
    }
}
void CalibService::SetCurResStatus(int id, bool isRes) {
    for (auto& i : mDeviceVec) {
        if (i.first == id) {
            i.second = isRes;
            MLOG_I("SetCurResStatus cur request id:{} res status:{}", i.first, i.second);
        }
    }
}
bool CalibService::CheckRespResult() {
    for (auto& i : mDeviceVec) {
        MLOG_I("CheckRespResult id:{} res:{}", i.first, i.second);
        if (!i.second) {
            return false;
        }
    }
    return true;
}
void CalibService::DumpInfo(minieye::calib::CalibRequestMsg msg) {
    std::stringstream ss;
    ss << "CalibInterfaceImpl::DumpInfo" << std::endl;
    ss << "CalibRequestMsg:" << std::endl;
    ss << "  calibType:" << msg.calibType << std::endl;
    ss << "  workStation:" << msg.workStation << std::endl;
    ss << "  calibNum:" << msg.calibNum << std::endl;
    ss << "  deviceMsg: {" << std::endl;
    for (int i = 1; i < minieye::calib::E_DEVICE_ID_MAX; i++) {
        if (msg.deviceMsg[i].deviceId == 0) {
            continue;
        }
        ss << "    [" << std::endl;
        ss << "      cmd:" << msg.deviceMsg[i].cmd << std::endl;
        ss << "      deviceId:" << msg.deviceMsg[i].deviceId << std::endl;
        ss << "    ]" << std::endl;
    }
    ss << "}" << std::endl;
    // std::cout << ss.str() << std::endl;
    MLOG_I("{}", ss.str());
}
