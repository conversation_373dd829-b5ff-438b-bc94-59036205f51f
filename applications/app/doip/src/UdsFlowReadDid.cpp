#include "interface/UdsFlowInterface.h"

#include <iostream>
#include <fstream>
#include <iomanip>
#include <sstream>

#include <openssl/sha.h>

#include "Launcher.h"

using namespace minieye::dolink;

// VehicleManufacturerSparePartNumber
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF187_Read_Hdler(std::vector<char> &value) {
    value.assign({'1', '8', '0', '0', '0', '0', '5', '7', 'A', 'A'});
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// SystemSupplierIdentifier
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF18A_Read_Hdler(std::vector<char> &value) {
    value.assign({'P', '1', '0', '2', '8', '7'});
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// SystemName
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF197_Read_Hdler(std::vector<char> &value) {
    value.assign({'J', '6', 'M', '_', 'A', 'D', 'C', 'U', ' ', ' '});
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ECUHardwareVersionNumber
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF193_Read_Hdler(std::vector<char> &value) {
    MLOG_I("Data_0xF193_Read_Hdler");
    // 0-1: Revision 修订版本 (BCD)
    VEHICLE_INFO_SERVICE()->GetECUHardwareVersion(value);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ECUSoftwareVersionNumber
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF195_Read_Hdler(std::vector<char> &value) {
    // 0-1: PhaseVersionNum       (ASCII)
    // 2  : _(0x5F)               (ASCII)
    // 3-4: MajorVersion          (BCD)
    // 5  : _(0x5F)               (ASCII)
    // 6-7: MinorVersion          (BCD)
    // PhaseVersionNum_MajorVersion_MinorVersion
    // => V3_0001_0001
    VEHICLE_INFO_SERVICE()->GetECUSoftwareVersion(value);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ECUSerialNumber
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF18C_Read_Hdler(std::vector<char> &value) {
    // 0-5 : SupplierCode
    // 6   : Year
    // 7   : Month
    // 8-17: ProductionLotNumber

    VEHICLE_INFO_SERVICE()->GetSN(value);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// VIN
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF190_Read_Hdler(std::vector<char> &value) {
    VEHICLE_INFO_SERVICE()->GetVIN(value);

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// SoftwareNumber
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF188_Read_Hdler(std::vector<char> &value) {
    value.assign({'S', '1', '8', '0', '0', '0', '1', '7', 'A', 'A'});
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// HardwareNumber
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF191_Read_Hdler(std::vector<char> &value) {
    value.assign({'1', '8', '0', '0', '0', '0', '5', '8', 'A', 'A'});
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ECUManufactureDate
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF18B_Read_Hdler(std::vector<char> &value) {
    // Y M D
    VEHICLE_INFO_SERVICE()->GetECUManufactureDate(value);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// Fingerprint
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF15A_Read_Hdler(std::vector<char> &value) {
    // 0-2: Y M D
    // 3  : TesterSerialNumberType
    // 4-8: TesterSerialNumberNum
    VEHICLE_INFO_SERVICE()->GetFingerprint(value);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// EquipmentMatintenanceFingerprintInfo
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF103_Read_Hdler(std::vector<char> &value) {
    // 0-2: Y M D
    // 3  : TesterSerialNumberType
    // 4-8: TesterSerialNumberNum
    VEHICLE_INFO_SERVICE()->GetEquipmentMatintenanceFingerprint(value);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// CurrentSessionType
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF186_Read_Hdler(std::vector<char> &value) {
    /**
     * 用于查询当前所处于的会话等级
     * 01 默认
     * 02 编程
     * 03 扩展
     * 40 OTA
     */
    value.push_back(static_cast<char>(SESSION_SERVICE()->GetCurSession()));
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// OtaFileHashValue
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xDF01_Read_Hdler(std::vector<char> &value) {
    /**
     * 文件不存在或完整性flag未被置位则返回全0，就没必要走hash计算，节省资源
     * HASH value（SHA256）32字节
     */
    value.assign(32, 0xFF);

    if (OTA_SERVICE()->GetCurMode() != OtaService::E_OTA_MODE_OTA_FILE) {
        MLOG_E("Just support ota mode: OTA_FILE !");
        return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
    }

    MLOG_D("OTA MODE: OTA_FILE> Read ota file hash value ...");

    if (OTA_SERVICE()->File()->Existed() == false) {
        MLOG_E("Not exist file!");
    } else if (OTA_SERVICE()->File()->GetIntegrityFlag() == false) {
        MLOG_E("Not exist Integrity file!");
    } else {
        std::vector<char> out_hex_key;
        if (OTA_SERVICE()->File()->CalHashKey(out_hex_key) == false) {
            MLOG_E("open OTA file error !");
            return minieye::uds::E_FLOW_READ_DID_HDLER_RET_GR_ERROR;
        }
        value.assign(out_hex_key.begin(), out_hex_key.end());
    }

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ECUBackupStatus
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xD0D3_Read_Hdler(std::vector<char> &value) {
    // TODO(zyx)
    // 0: bad
    // 1: ok
    value = {0};
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ECUBackupSoftwareVersionNumber
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF1A0_Read_Hdler(std::vector<char> &value) {
    // TODO(zyx)
    // 0-1: PhaseVersionNum
    // 2  : _(0x5F)
    // 3-4: MajorVersion
    // 5  : _(0x5F)
    // 6-7: MinorVersion
    // PhaseVersionNum_MajorVersion_MinorVersion
    value = {0x20, 0x20, 0x5F, 0x00, 0x01, 0x5F, 0x00, 0x01};
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ECUBackupSoftwareNumber
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF1A1_Read_Hdler(std::vector<char> &value) {
    // TODO(zyx)
    value.assign(10, 0x20);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ADCU com sercet request status ADCU通信密钥申请状态
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xD43D_Read_Hdler(std::vector<char> &value) {
    // TODO(zyx)
    // 0x0: Virgin                        0x0: 未申请过
    // 0x1: get ADCU com secret fail      0x1: ADCU 通信密钥申请失败
    // 0x2: get ADCU com key successly    0x2: ADCU 通信密钥申请成功
    // 0x3: ADCU com key overdue          0x3: ADCU 通信密钥过期

    auto sts = ADACU_SERVICE()->GetStatus();
    // 未申请&申请中为内部状态
    value.push_back(sts < AdacuService::E_STATUS_REGISTER_EXPIRED ? sts : AdacuService::E_STATUS_UNKNOWN);

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// Vehicle configuration
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF101_Read_Hdler(std::vector<char> &value) {
    VEHICLE_INFO_SERVICE()->GetVehicleConfiguration(value);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// ADCU A/B Status
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xD43E_Read_Hdler(std::vector<char> &value) {
    int32_t slot;
    if (!OTA_SERVICE()->GetCurSlot(slot)) {
        MLOG_E("Get cur slot failed!");
        return minieye::uds::E_FLOW_READ_DID_HDLER_RET_GR_ERROR;
    }
    value = {slot == 1 ? 'B' : 'A'};

    MLOG_D("Read ADCU status: {}", value[0]);

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// Soft Version
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF1F1_Read_Hdler(std::vector<char> &value) {
    char buff[256]{};
    int32_t size = 0;

    size = minieye::system::file::ReadFile("/etc/mi_version", buff, sizeof(buff), 0);
    if (size < 0) {
        MLOG_E("Read version file failed!");
        return minieye::uds::E_FLOW_READ_DID_HDLER_RET_GR_ERROR;
    }

    value.assign(std::begin(buff), std::begin(buff) + size - 1);
    while (value.size() < 32) {
        value.push_back(0x20);
    }

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// MCU Version
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF1F2_Read_Hdler(std::vector<char> &value) {
    static const char path[] = "/sys/class/remoteproc/remoteproc0/mcu_version";
    static const char type_key[] = "MCU Board type = ";
    static const char time_key[] = "MCU Build time = ";
    static const std::array<std::string, 12> month_list = {
        "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

    std::fstream fs(path);
    if (!fs.is_open()) {
        MLOG_E("Open {} failed !", path);
        return minieye::uds::E_FLOW_READ_DID_HDLER_RET_GR_ERROR;
    }
    std::string line, mcu_ver, mcu_time;
    auto index = std::string::npos;

    while (fs) {
        std::getline(fs, line);
        if ((index = line.find(type_key)) != std::string::npos) {
            mcu_ver = line.substr(index + strlen(type_key));
        } else if ((index = line.find(time_key)) != std::string::npos) {
            mcu_time = line.substr(index + strlen(time_key));
        }
    }

    if (mcu_ver.empty() || mcu_time.empty()) {
        MLOG_E("Read MCU version file failed!");
        return minieye::uds::E_FLOW_READ_DID_HDLER_RET_GR_ERROR;
    }

    for (auto &i : mcu_ver) {
        value.push_back(i);
    }
    value.push_back('_');

    char ver_time[15]{}, month[3]{};
    int year, day, hour, minute, sec;

    sscanf(mcu_time.c_str(), "%s %d %d %d:%d:%d", month, &day, &year, &hour, &minute, &sec);
    auto month_iter = std::find(month_list.begin(), month_list.end(), month);
    sprintf(ver_time,
            "%d%02d%02d%02d%02d%02d",
            year,
            static_cast<int32_t>(month_iter - month_list.begin()) + 1,
            day,
            hour,
            minute,
            sec);

    for (auto &i : ver_time) {
        if (i != 0) {
            value.push_back(i);
        }
    }

    while (value.size() < 32) {
        value.push_back(0x20);
    }

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// DBC_Version
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF12A_Read_Hdler(std::vector<char> &value) {
    // 0-1: PhaseVersionNum
    // 2  : _(0x5F)
    // 3-4: MajorVersion
    // 5  : _(0x5F)
    // 6-7: MinorVersion
    value.assign({'V', '6', '_', '0', '0', '_', '0', '1'});
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// DDS_Version
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF12B_Read_Hdler(std::vector<char> &value) {
    // 0-1: PhaseVersionNum
    // 2  : _(0x5F)
    // 3-4: MajorVersion
    // 5  : _(0x5F)
    // 6-7: MinorVersion
    value.assign({'V', '1', '_', '1', '7', '_', '0', '8'});
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

// Bootloader Version Number
minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xF180_Read_Hdler(std::vector<char> &value) {
    value.assign({'V', '1', '.', '0', '0', '.', '0', '0'});
    while (value.size() < 16) {
        value.push_back(' ');
    }
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}


minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xCF00_Read_Hdler(std::vector<char> & value) {
    // 0: Battery Voltage (cvt: phy=xx*0.1)

    auto _val = static_cast<char>(VEHICLE_STATUS_SERVICE()->GetBatteryVoltage() / 0.1) ;
    value.push_back(_val);

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xCF01_Read_Hdler(std::vector<char> & value) {
    // 0-1: Vehicle Speed (cvt: phy=xx*0.05625)

    auto _val = static_cast<int16_t>(VEHICLE_STATUS_SERVICE()->GetVehicleSpeed() / 0.05625);
    value.assign({
        static_cast<char>((_val >> 8) & 0xFF),
        static_cast<char>((_val >> 0) & 0xFF)
    });

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xCF02_Read_Hdler(std::vector<char> & value) {
    // 0:	AbsoluteTime_Year
    // 1:	AbsoluteTime_Month
    // 2:	AbsoluteTime_Day
    // 3:	AbsoluteTime_Hour
    // 4:	AbsoluteTime_Minute
    // 5:	AbsoluteTime_Second

    auto _val = VEHICLE_STATUS_SERVICE()->GetAbsTime();
    value.assign({_val.year, _val.month, _val.day, _val.hour, _val.min, _val.sec});

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0xCF03_Read_Hdler(std::vector<char> & value) {
    // 0-3: Odometer (cvt: phy=xx*0.1)

    auto _val = static_cast<int32_t>(VEHICLE_STATUS_SERVICE()->GetOdometer() / 0.1);
    value.assign({
        static_cast<char>((_val >> 16) & 0xFF),
        static_cast<char>((_val >> 8) & 0xFF),
        static_cast<char>((_val >> 0) & 0xFF)
    });

    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}

minieye::uds::FlowReadDidHdlerRet_t interface::DID::Data_0x4F02_Read_Hdler(std::vector<char> & value) {
    VEHICLE_INFO_SERVICE()->GetEnv(value);
    return minieye::uds::E_FLOW_READ_DID_HDLER_RET_SUCCESS;
}