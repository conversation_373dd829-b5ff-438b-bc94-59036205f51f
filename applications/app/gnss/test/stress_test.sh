#!/bin/bash

# 配置参数
GNSS_DEV="/dev/ttyS1"
IMU_DEV="/dev/ttyS2"
LOG_FILE="/userdata/combined_stress_test.log"
DEFAULT_TIMEOUT=5

# 全局变量
RUNNING=true

# 信号处理
cleanup() {
    echo "Received interrupt signal, cleaning up..."
    RUNNING=false
    write_log "combined stress test stopped"
    echo "Combined stress test stopped."
    
    # 清理后台进程
    jobs -p | xargs -r kill 2>/dev/null
    exit 0
}

trap cleanup SIGINT SIGTERM

# 日志函数
write_log() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')
    echo "$timestamp - $message" >> "$LOG_FILE"
    echo "$timestamp - $message"
}

# 获取当前时间戳（秒）
get_current_time() {
    date +%s
}

# GNSS初始化
init_gnss() {
    write_log "Initializing GNSS device..."
    
    # GNSS GPIO初始化
    for gpio in 438 509 510 441; do
        if [ ! -d /sys/class/gpio/gpio$gpio ]; then
            echo $gpio > /sys/class/gpio/export 2>/dev/null
        fi
    done

    echo in > /sys/class/gpio/gpio509/direction 2>/dev/null
    echo out > /sys/class/gpio/gpio438/direction 2>/dev/null
    echo 0 > /sys/class/gpio/gpio438/value 2>/dev/null
    echo out > /sys/class/gpio/gpio510/direction 2>/dev/null
    echo 0 > /sys/class/gpio/gpio510/value 2>/dev/null
    echo out > /sys/class/gpio/gpio441/direction 2>/dev/null
    echo 0 > /sys/class/gpio/gpio441/value 2>/dev/null

    sleep 2
    echo 1 > /sys/class/gpio/gpio438/value 2>/dev/null
    sleep 0.2
    echo 1 > /sys/class/gpio/gpio510/value 2>/dev/null
    sleep 0.1
    echo 1 > /sys/class/gpio/gpio441/value 2>/dev/null

    # 配置串口
    stty -F $GNSS_DEV 460800 cs8 raw -parenb -cstopb -crtscts 2>/dev/null
    
    write_log "GNSS device initialized"
}

# IMU初始化
init_imu() {
    write_log "Initializing IMU device..."
    
    # IMU GPIO初始化
    if [ ! -d /sys/class/gpio/gpio478 ]; then 
        echo 478 > /sys/class/gpio/export 2>/dev/null
    fi
    echo out > /sys/class/gpio/gpio478/direction 2>/dev/null
    echo 0 > /sys/class/gpio/gpio478/value 2>/dev/null

    sleep 2
    echo 1 > /sys/class/gpio/gpio478/value 2>/dev/null

    # 配置串口
    stty -F $IMU_DEV 230400 cs8 raw -parenb -cstopb -crtscts 2>/dev/null
    
    write_log "IMU device initialized"
}

# 发送配置命令
send_config_commands() {
    # 发送GNSS配置命令
    if [ -w "$GNSS_DEV" ]; then
        echo -e "AGGNRMC,0,1,10\r\n" > $GNSS_DEV 2>/dev/null
        write_log "GNSS config command sent: AGGNRMC,0,1,10"
    fi
    
    # 发送IMU配置命令
    if [ -w "$IMU_DEV" ]; then
        echo -e "CONFIG_IMU_SAMPLE_RATE 100\r\n" > $IMU_DEV 2>/dev/null
        write_log "IMU config command sent: CONFIG_IMU_SAMPLE_RATE 100"
    fi
}

# 监控设备数据
monitor_devices() {
    local timeout_sec=$1
    local gnss_last_time=$(get_current_time)
    local imu_last_time=$(get_current_time)
    local gnss_timeout_logged=false
    local imu_timeout_logged=false
    local last_log_time=$(get_current_time)
    
    # 启动数据读取进程
    (
        while $RUNNING; do
            if timeout 0.5 head -c 1 $GNSS_DEV >/dev/null 2>&1; then
                echo "GNSS_DATA_$(date +%s)" > /tmp/gnss_activity
            fi
        done
    ) &
    
    (
        while $RUNNING; do
            if timeout 0.5 head -c 1 $IMU_DEV >/dev/null 2>&1; then
                echo "IMU_DATA_$(date +%s)" > /tmp/imu_activity
            fi
        done
    ) &
    
    # 主监控循环
    while $RUNNING; do
        local current_time=$(get_current_time)
        
        # 检查GNSS活动
        if [ -f /tmp/gnss_activity ]; then
            gnss_last_time=$(cat /tmp/gnss_activity | cut -d'_' -f3)
            rm -f /tmp/gnss_activity
            gnss_timeout_logged=false
            echo "GNSS data activity detected"
        fi
        
        # 检查IMU活动
        if [ -f /tmp/imu_activity ]; then
            imu_last_time=$(cat /tmp/imu_activity | cut -d'_' -f3)
            rm -f /tmp/imu_activity
            imu_timeout_logged=false
            echo "IMU data activity detected"
        fi
        
        # 检查GNSS超时
        local gnss_diff=$((current_time - gnss_last_time))
        if [ $gnss_diff -ge $timeout_sec ]; then
            if [ "$gnss_timeout_logged" = false ] || [ $((current_time - last_log_time)) -ge 1 ]; then
                write_log "no gnss data $gnss_diff seconds"
                gnss_timeout_logged=true
                last_log_time=$current_time
            fi
        fi
        
        # 检查IMU超时
        local imu_diff=$((current_time - imu_last_time))
        if [ $imu_diff -ge $timeout_sec ]; then
            if [ "$imu_timeout_logged" = false ] || [ $((current_time - last_log_time)) -ge 1 ]; then
                write_log "no imu data $imu_diff seconds"
                imu_timeout_logged=true
                last_log_time=$current_time
            fi
        fi
        
        sleep 1
    done
}

# 主函数
main() {
    local timeout_sec=${1:-$DEFAULT_TIMEOUT}
    
    # 验证超时参数
    if ! [[ "$timeout_sec" =~ ^[0-9]+$ ]] || [ "$timeout_sec" -le 0 ]; then
        echo "Invalid timeout parameter, using default $DEFAULT_TIMEOUT seconds"
        timeout_sec=$DEFAULT_TIMEOUT
    fi
    
    write_log "combined stress test start, timeout: ${timeout_sec} seconds"
    echo "Combined GNSS and IMU stress test started with timeout: $timeout_sec seconds"
    
    # 检查设备存在
    if [ ! -e "$GNSS_DEV" ]; then
        write_log "GNSS device $GNSS_DEV not found"
        echo "Error: GNSS device $GNSS_DEV not found"
        exit 1
    fi
    
    if [ ! -e "$IMU_DEV" ]; then
        write_log "IMU device $IMU_DEV not found"
        echo "Error: IMU device $IMU_DEV not found"
        exit 1
    fi
    
    # 初始化设备
    init_gnss
    init_imu
    
    # 等待设备稳定
    sleep 10
    
    # 发送配置命令
    send_config_commands
    
    echo "Monitoring started. Press Ctrl+C to exit."
    
    # 开始监控
    monitor_devices $timeout_sec
}

# 脚本入口
if [ $# -gt 1 ]; then
    echo "Usage: $0 [timeout_seconds]"
    echo "Default timeout: $DEFAULT_TIMEOUT seconds"
    exit 1
fi

# 确保日志目录存在
mkdir -p "$(dirname "$LOG_FILE")"

main "$@"
