/*
 * Copyright [2023] MINIEYE
 * Descripttion : GNSS接口使用示例
 * Author       : xu<PERSON>esen
 * Date         : 2025-04-02
 */

#include "GnssDevice.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <atomic>
#include <cstring>
#include <sstream>
#include <fstream>
#include <iomanip>
#include <mutex>

std::atomic<bool> gRunning(true);
std::atomic<std::chrono::steady_clock::time_point> gLastDataTime(std::chrono::steady_clock::now());
std::mutex gLogMutex;
const std::string LOG_FILE_PATH = "/userdata/gnss_stress_test.log";

void SignalHandler(int signum) {
    std::cout << "Interrupt signal (" << signum << ") received." << std::endl;
    gRunning = false;
}

std::string GetCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

// 写入日志文件
void WriteLog(const std::string& message) {
    std::lock_guard<std::mutex> lock(gLogMutex);
    std::ofstream logFile(LOG_FILE_PATH, std::ios::app);
    if (logFile.is_open()) {
        logFile << GetCurrentTimeString() << " - " << message << std::endl;
        logFile.close();
    }
}



// GNSS数据回调函数
void GnssDataCallback(const char* data, int32_t dataLen) {
    gLastDataTime.store(std::chrono::steady_clock::now());
}

int main(int argc, char* argv[]) {
    signal(SIGINT, SignalHandler);

    int timeoutSec = 5;
    if (argc > 1) {
        try {
            timeoutSec = std::stoi(argv[1]);
            if (timeoutSec <= 0) {
                std::cerr << "Timeout must be positive, using default 5 seconds" << std::endl;
                timeoutSec = 5;
            }
        } catch (...) {
            std::cerr << "Invalid timeout parameter, using default 5 seconds" << std::endl;
            timeoutSec = 5;
        }
    }

    WriteLog("gnss stress test start, timeout: " + std::to_string(timeoutSec) + " seconds");
    std::cout << "GNSS stress test started with timeout: " << timeoutSec << " seconds" << std::endl;

    minieye::gnss::GnssDevice& gnss = minieye::gnss::GnssDevice::GetInstance();

    if (!gnss.Init(GnssDataCallback)) {
        std::cerr << "Failed to initialize GNSS module" << std::endl;
        WriteLog("Failed to initialize GNSS module");
        return -1;
    }

    // 设置GNSS，通过串口 0 输出位置和速度信息，频率 10Hz
    const char* cmdData = "AGGNRMC,0,1,10\r\n";
    gnss.WriteData(cmdData, static_cast<int32_t>(strlen(cmdData)));

    std::cout << "GNSS module is running. Press Ctrl+C to exit." << std::endl;

    auto timeout = std::chrono::seconds(timeoutSec);
    auto lastLogTime = std::chrono::steady_clock::now();
    bool hasLoggedTimeout = false;

    while (gRunning) {
        auto now = std::chrono::steady_clock::now();
        auto lastData = gLastDataTime.load();
        auto timeSinceLastData = now - lastData;

        if (timeSinceLastData >= timeout) {
            if (!hasLoggedTimeout || (now - lastLogTime) >= std::chrono::seconds(1)) {
                auto timeoutSeconds = std::chrono::duration_cast<std::chrono::seconds>(timeSinceLastData).count();
                std::string logMessage = "no gnss data " + std::to_string(timeoutSeconds) + " seconds";
                WriteLog(logMessage);
                std::cout << "Timeout: " << logMessage << std::endl;

                lastLogTime = now;
                hasLoggedTimeout = true;
            }
        } else {
            hasLoggedTimeout = false;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    gnss.Terminate();
    WriteLog("gnss stress test stopped");
    std::cout << "GNSS sample application exited." << std::endl;
    return 0;
}
