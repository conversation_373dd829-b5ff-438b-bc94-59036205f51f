/*
 * Copyright [2023] MINIEYE
 * Descripttion : GNSS接口使用示例
 * Author       : xu<PERSON><PERSON>n
 * Date         : 2025-04-02
 */

#include "GnssDevice.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <atomic>
#include <cstring>
#include <sstream>

std::atomic<bool> gRunning(true);

void SignalHandler(int signum) {
    std::cout << "Interrupt signal (" << signum << ") received." << std::endl;
    gRunning = false;
}

/*
    当只有 GPS 卫星系统参与解算时，以 GPRMC 输出；只有 BDS 卫星系统参
    与解算时，以 GBRMC 输出；只有 GLONASS 卫星系统参与解算时，以 GLRMC 输出；
    只有 GALILEO 卫星系统参与解算时，以 GARMC 输出；只有 QZSS 卫星系统参与解算
    时，以 GQRMC 输出；有两个及以上卫星系统参与解算时，以 GNRMC 输出。

    GNSS 定位数据输出格式：
        $GNRMC,022509.000,A,3112.468204,N,12118.544387,E,0.3,1.8,190324,,,A,V*0E\r\n

    GPRMC, GBRMC, GLRMC, GARMC, GQRMC, GNRMC
*/
bool CheckRmcValid(const char* data, int32_t dataLen) {
    if (data == nullptr || dataLen < 6 || data[0] != '$' || data[3] != 'R' || data[4] != 'M' || data[5] != 'C') {
        return false;
    }

    auto pCheckSum = strstr(data, "*");
    auto pStart = strstr(data, "$") + 1;
    auto pEnd = strstr(data, "\r");
    if (pCheckSum + 3 != pEnd) {
        return false;
    }
    try {
        auto checkSum = std::stoi(pCheckSum + 1, nullptr, 16);
        int checkSum2 = 0;
        for (auto p = pStart; p < pCheckSum; p++) {
            checkSum2 ^= static_cast<unsigned char>(*p);
        }
        std::cout << "checksum = " << checkSum << ' ' << checkSum2 << std::endl;
        return checkSum == checkSum2;

    } catch (...) {
    }
    return false;
}

bool IsAllDigits(const std::string& s) {
    return !s.empty() && std::all_of(s.begin(), s.end(), ::isdigit);
}

// 使用日期、时间转换成UTC时间戳
bool ParseUtcMillis(const std::vector<std::string>& tokens, uint64_t& millTs) {
    if (tokens.size() < 10) {
        return false;
    }

    const std::string& time_str = tokens[1];
    size_t dot_pos = time_str.find('.');
    std::string hms_part = (dot_pos != std::string::npos) ? time_str.substr(0, dot_pos) : time_str;
    std::string millis_str = (dot_pos != std::string::npos) ? time_str.substr(dot_pos + 1) : "000";

    if (hms_part.size() != 6 || !IsAllDigits(hms_part) || millis_str.empty()) {
        return false;
    }

    millis_str.resize(3, '0');
    if (!IsAllDigits(millis_str)) {
        return false;
    }

    const std::string& date_str = tokens[9];
    if (date_str.size() != 6 || !IsAllDigits(date_str)) {
        return false;
    }

    try {
        int hh = std::stoi(hms_part.substr(0, 2));
        int mm = std::stoi(hms_part.substr(2, 2));
        int ss = std::stoi(hms_part.substr(4, 2));
        int millis = std::stoi(millis_str);

        int dd = std::stoi(date_str.substr(0, 2));
        int mon = std::stoi(date_str.substr(2, 2));
        int yy = std::stoi(date_str.substr(4, 2));

        struct tm time_tm = {};
        time_tm.tm_year = 2000 + yy - 1900;
        time_tm.tm_mon = mon - 1;
        time_tm.tm_mday = dd;
        time_tm.tm_hour = hh;
        time_tm.tm_min = mm;
        time_tm.tm_sec = ss;

        time_t time_utc = timegm(&time_tm);

        if (time_utc == -1) {
            return false;
        }

        // 计算毫秒时间戳
        millTs = static_cast<uint64_t>(time_utc) * 1000 + millis;
        return true;
    } catch (...) {
        return false;
    }
}

// GNSS数据回调函数
void GnssDataCallback(const char* data, int32_t dataLen) {
    std::string command(data, dataLen);
    std::cout << "Received GNSS data, length: " << dataLen << std::endl;
    std::cout << "Data: " << command << std::endl;

    // command =
    //     "$GNRMC,,V,,,,,,,,,,V,V*2F\r\n"
    //     "$GNRMC,,V,,,,,,,,,,V,V*2F\r\n"
    //     "$GNRMC,,V,,,,,,,,,,V,V*2F\r\n"
    //     "$GNRMC,,V,,,,,,,,,,V,V*2F\r\n"
    //     "$GNGGA,,,,,,0,,,,,,,,*78\r\n"
    //     "$GNGGA,,,,,,0,,,,,,,,*78\r\n"
    //     "$GNRMC,,V,,,,,,,,,,V,V*2F\r\n";
    // 实际应用中，这里应该解析GNSS数据，并进行相应处理

    std::string::size_type begin = 0;
    std::string::size_type pos = begin;
    while (pos = command.find("\r\n", begin), pos != std::string::npos) {
        // 示例：设置GNSS定位时间
        pos += 2;
        std::string line = command.substr(begin, pos - begin);
        begin = pos;
        std::cout << "solveing line: " << line;
        if (CheckRmcValid(line.c_str(), line.size())) {
            minieye::gnss::GnssDevice& gnss = minieye::gnss::GnssDevice::GetInstance();
            uint64_t millTs = 0;

            std::stringstream ss(line);
            std::vector<std::string> tokens;
            std::string token;
            while (getline(ss, token, ',')) {
                tokens.push_back(token);
            }

            if (tokens.size() >= 8) {
                auto valid = (tokens[2] == "A");
                auto lat = tokens[3];
                auto lon = tokens[5];
                auto speed = tokens[7];
                std::cout << "valid = " << valid << " lat = " << lat << " lon = " << lon << " speed = " << speed
                          << std::endl;

                // utc时间戳
                if (ParseUtcMillis(tokens, millTs)) {
                    gnss.SetGnssTime(millTs);
                    std::cout << "Set GNSS time: " << millTs << std::endl;
                }

            } else {
                std::cout << "invalid tokens size: " << tokens.size() << std::endl;
            }
        }
        std::cout << "----------------------------------------------------" << std::endl;
    }
}

int main(int argc, char* argv[]) {
    signal(SIGINT, SignalHandler);

    // 获取GNSS管理器实例
    minieye::gnss::GnssDevice& gnss = minieye::gnss::GnssDevice::GetInstance();

    // 初始化GNSS模块，传入回调函数
    if (!gnss.Init(GnssDataCallback)) {
        std::cerr << "Failed to initialize GNSS module" << std::endl;
        return -1;
    }

    // 设置GNSS，通过串口 0 输出位置和速度信息，频率 1Hz
    const char* cmdData = "AGGNRMC,0,1,1\r\n";
    gnss.WriteData(cmdData, static_cast<int32_t>(strlen(cmdData)));

    std::cout << "GNSS module is running. Press Ctrl+C to exit." << std::endl;

    // 主循环
    int loopCount = 0;
    while (gRunning) {
        std::this_thread::sleep_for(std::chrono::seconds(5));

        // 每隔30秒模拟发送一次数据
        if (++loopCount % 6 == 0) {
            const char* periodicCmd = "$PQTMVERNO*58\r\n";  // 示例命令：查询版本信息
            gnss.WriteData(periodicCmd, static_cast<int32_t>(strlen(periodicCmd)));
        }

        if (loopCount % 10 == 0) {
            // 重置Gnss
            gnss.Reset();
        }
    }

    gnss.Terminate();

    std::cout << "GNSS sample application exited." << std::endl;
    return 0;
}
