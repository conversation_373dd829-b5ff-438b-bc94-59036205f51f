import("//build/minieye/minieye.gni")

minieye_group("gnss_group") {
    deps = [
        ":gnss",
        ":gnss_sample",
        ":gnss_test",
        ":gnss_demo",
    ]

}

app_cmm_dir = rebase_path("//applications/app/common/")

minieye_shared_library("gnss") {
    sources = [
        "${app_cmm_dir}/dbc/src/IPC_matrix_Middleware.c",
        "${app_cmm_dir}/timehal/src/minieye_time_hal.cpp",
        "src/GnssDevice.cpp",
        "src/TtyDevice.cpp",
        "src/GpioCtrl.cpp",
        "src/CanIo.cpp",
    ]

    include_dirs = [
        "${app_cmm_dir}/dbc/inc",
        "${app_cmm_dir}/timehal/include",
        "include",
        "src",
    ]
    cflags_cc = [
        "-Wall",
        "-Wno-deprecated-declarations",
    ]
    ldflags = [
        "-lhbtimesynchal",
    ]


    deps = [
        "//middleware/mlog:libmlog",
        "//middleware/system/core/libcppbase:cppbase",
    ]
}

minieye_executable("gnss_sample") {
    sources = [
        "sample/main.cpp",
    ]

    include_dirs = [
        "include",
    ]

    deps = [
        ":gnss",
    ]
}

minieye_executable("gnss_test") {
    sources = [
        "test/main.cpp",
    ]

    include_dirs = [
        "include",
    ]

    ldflags = [
        "-lgtest",
        "-lgtest_main",
    ]

    deps = [
        ":gnss",
    ]
}

minieye_executable("gnss_demo") {
    sources = [
        "test/demo.cpp",
    ]

    include_dirs = [
        "include",
    ]

    deps = [
        ":gnss",
    ]
}

minieye_prebuilt_etc("gnss_tools") {
    source = "ota/bin/gnss_tools"
    relative_install_dir = "bin"
}

minieye_prebuilt_etc("module_ota") {
    source = "ota/bin/module_ota"
    relative_install_dir = "bin"
}

minieye_prebuilt_etc("gnss_firmware") {
    source = "ota/firmware/gnss/NAV3120SAA-VB1.02.08.00.00.00-2b9eedf.pkg"
    relative_install_dir = "bin"
}

minieye_prebuilt_etc("gnss_bootloader") {
    source = "ota/firmware/gnss/delos_utility_460800_v454.pkg"
    relative_install_dir = "bin"
}

minieye_prebuilt_etc("ota_sdk") {
    source = "ota/lib/libAsensingOtaSDK.so"
    relative_install_dir = "."
}