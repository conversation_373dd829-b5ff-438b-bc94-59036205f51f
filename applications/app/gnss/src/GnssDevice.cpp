#include "GnssDevice.h"

#include "IPC_matrix_Middleware.h"
#include "TtyDevice.h"
#include "CanIo.h"
#include "GpioCtrl.h"
#include "hobot_clock_hal.h"

namespace minieye {
namespace gnss {

#define GNSS_PWR_GPIO_IDX    (438)
#define GNSS_RST_PIN_IDX     (510)
#define GNSS_DATA_EN_PIN_IDX (441)
#define GNSS_PPS_OUT_PIN_IDX (509)
#define GNSS_DEVICE_BAUDRATE (460800)
#define GNSS_DEVICE_NAME     "/dev/ttyS1"
#define GNSS_BUFTAG          "gnssData"

template<int ExpireTime = 60, int NoDataReportTime = 10, int ReportInterval = ExpireTime>
class DataStatics {
 public:
    DataStatics()
        : mTotLens(0)
        , mLastDataTime(0) {
    }
    void start() {
        if (mIsRunning) {
            return;
        }
        mIsRunning = true;
        mDataStaticsThread = std::thread([this]() {
            while (mIsRunning) {
                {
                    std::lock_guard<std::mutex>lg(mMtx);
                    cleanExpireData();
                    // 错误统计
                    for (auto& [faultType, faultInfo] : mFaultMap) {
                        const auto& [faultCnt, faultTime] = faultInfo;
                        if (minieye::system::GetUptimeSec() - faultTime < ExpireTime &&
                            minieye::system::GetUptimeSec() - mLastReportTimeMap[faultType] > ReportInterval) {
                            MLOG_W("{}", getFaultReport());
                            mLastReportTimeMap[faultType] = minieye::system::GetUptimeSec();
                        }
                    }
                    // 无数据统计
                    if (minieye::system::GetUptimeSec() - mLastDataTime > NoDataReportTime &&
                        minieye::system::GetUptimeSec() - mLastReportTimeMap["noData"] > ReportInterval) {
                        MLOG_W("{}", getNoDataReport());
                        mLastReportTimeMap["noData"] = minieye::system::GetUptimeSec();
                    }
                    // 统计信息
                    if (minieye::system::GetUptimeSec() - mLastReportTimeMap["statistics"] > ReportInterval) {
                        MLOG_I("{}", getStatistics());
                        mLastReportTimeMap["statistics"] = minieye::system::GetUptimeSec();
                    }
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
            MLOG_I("mDataStaticsThread exiting!!");
        });
    }

    void addData(uint32_t len) {
        std::lock_guard<std::mutex> lg(mMtx);
        mLastDataTime = minieye::system::GetUptimeSec();
        mTotLens += len;
        mDataQueue.emplace_back(std::make_pair(mLastDataTime, len));
    }

    void addFault(int32_t faultType) {
        std::string faultStr = "fault_type = " + std::to_string(faultType);
        addFault(faultStr);
    }

    void addFault(std::string faultType) {
        std::lock_guard<std::mutex> lg(mMtx);
        if (mFaultMap.find(faultType) != mFaultMap.end()) {
            mFaultMap[faultType] = std::make_pair(mFaultMap[faultType].first + 1, minieye::system::GetUptimeSec());
        } else {
            mFaultMap[faultType] = std::make_pair(1, minieye::system::GetUptimeSec());
        }
    }

    void reset() {
        {
            std::lock_guard<std::mutex> lg(mMtx);
            mIsRunning = false;
        }
        if (mDataStaticsThread.joinable()) {
            mDataStaticsThread.join();
        }
        {
            std::lock_guard<std::mutex> lg(mMtx);
            mLastReportTimeMap.clear();
            mDataQueue.clear();
            mFaultMap.clear();
            mTotLens = 0;
            mLastDataTime = 0;
        }
    }

    std::string getStatistics() {
        std::string ret = "Last " + std::to_string(ExpireTime) + "s: dataCount: " + std::to_string(mDataQueue.size()) +
                          ", totLens: " + std::to_string(mTotLens) + ", lastDataTime: " + std::to_string(mLastDataTime);
        return ret;
    }

    std::string getNoDataReport() {
        auto noDataTime = minieye::system::GetUptimeSec() - mLastDataTime;
        return "No data received for " + std::to_string(noDataTime) + "s";
    }

    std::string getFaultReport() {
        std::string ret = "FaultList:";
        for (auto& it : mFaultMap) {
            ret += '\n' + it.first + "cnt: " + std::to_string(it.second.first) +
                   " last report at: " + std::to_string(it.second.second);
        }
        return ret;
    }

 private:
    void cleanExpireData() {
        auto curTime = minieye::system::GetUptimeSec();
        if (curTime > ExpireTime) {
            auto expireTime = curTime - ExpireTime;
            auto it = mDataQueue.begin();
            while (it != mDataQueue.end() && it->first < expireTime) {
                mTotLens -= it->second;
                it++;
            }
            mDataQueue.erase(mDataQueue.begin(), it);
        }
    }

    std::deque<std::pair<uint64_t /*timestamp*/, uint32_t /*len*/>> mDataQueue;
    std::map<std::string, std::pair<uint64_t /*fault cnt*/, uint64_t /*timestamp*/>> mFaultMap;
    uint64_t mTotLens;
    uint64_t mLastDataTime;

    std::mutex mMtx;
    std::map<std::string, uint64_t> mLastReportTimeMap;
    std::thread mDataStaticsThread;
    std::atomic<bool> mIsRunning = false;
};

static DataStatics dataStatics;
static std::unique_ptr<CanIo> canIo = nullptr;
static timesyncClock rtcClock;
static GnssDevice* instance = nullptr;

static uint32_t CalculateChecksum(const char *data, uint32_t len) {
    uint32_t checksum = 0;
    int i;
    for (i = 0; i < (int32_t)len; i++) {
        checksum += (uint32_t)data[i];
    }
    return checksum;
}

static bool SetupGnss() {
    static bool ret = false;
    if (ret) {
        return true;
    }
    ret = true;
    ret &= GpioCtrl::SetGpioDirection(GNSS_PPS_OUT_PIN_IDX, "in");
    ret &= GpioCtrl::SetGpioDirection(GNSS_PWR_GPIO_IDX, "out");
    ret &= GpioCtrl::SetGpio(GNSS_PWR_GPIO_IDX, 0);
    ret &= GpioCtrl::SetGpioDirection(GNSS_RST_PIN_IDX, "out");
    ret &= GpioCtrl::SetGpio(GNSS_RST_PIN_IDX, 0);
    ret &= GpioCtrl::SetGpioDirection(GNSS_DATA_EN_PIN_IDX, "out");
    ret &= GpioCtrl::SetGpio(GNSS_DATA_EN_PIN_IDX, 0);

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    ret &= GpioCtrl::SetGpio(GNSS_PWR_GPIO_IDX, 1);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    ret &= GpioCtrl::SetGpio(GNSS_RST_PIN_IDX, 1);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    ret &= GpioCtrl::SetGpio(GNSS_DATA_EN_PIN_IDX, 1);
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));

    return ret;
}

GnssDevice& GnssDevice::GetInstance() {
    if (instance == nullptr) {
        instance = new GnssDevice();
        minieye::mlog::LogConfig config;
        config.level = minieye::mlog::LogInfo;        // log level
        config.tag = "gnss";                          // log tag name
        config.isRemoteLog = true;                    // print to mlogcat or not
        config.isLogFile = false;                     // log file save or not
        config.logDomain = minieye::mlog::DomainApp;  // remote log buffer
        MLOG_INIT(config);
    }
    return *instance;
}

bool GnssDevice::Init(GnssDataCallback callback) {
    if (mIsRunning) {
        return mTtyDevice->AddDataCb(GNSS_BUFTAG, [callback](char const* data, int32_t len) {
            callback(data, len);
            dataStatics.addData(len);
        });
    }
    mTtyDevice = std::make_shared<TtyDevice>();
    canIo = std::make_unique<CanIo>(reinterpret_cast<const char*>("can_mcu"), true);
    if (mTtyDevice == nullptr || canIo == nullptr) {
        return false;
    }
    bool ret = SetupGnss();
    ret = ret && mTtyDevice->Init(GNSS_DEVICE_NAME, GNSS_DEVICE_BAUDRATE);
    ret = ret && mTtyDevice->AddDataCb(GNSS_BUFTAG, [callback](char const* data, int32_t len) {
        callback(data, len);
        dataStatics.addData(len);
    });
    ret = ret && mTtyDevice->Start(GNSS_BUFTAG);
    ret = ret && !timesyncOpenClock(CLOCK_EXRTC_32768_ID, &rtcClock);
    if (ret) {
        mIsRunning = true;
        dataStatics.start();
    }
    return ret;
}

bool GnssDevice::WriteData(const char* data, int32_t dataLen) {
    if (!mIsRunning || mTtyDevice == nullptr) {
        return false;
    }
    return mTtyDevice->Send((const char*)data, dataLen);
}

bool GnssDevice::SetGnssTime(int64_t millTs) {
    if (!mIsRunning || canIo == nullptr) {
        MLOG_E("SetGnssTime fail! mIsRunning = {}, canIo = {}", mIsRunning, canIo != nullptr);
        return false;
    }
    // utc转localtime
    struct tm tmLocal;
    time_t gnssUtc = millTs / 1000;
    localtime_r(&gnssUtc, &tmLocal);
    uint64_t gnssLocal = millTs + tmLocal.tm_gmtoff * 1000;

    struct canfd_frame canFdFrame;
    can_0x688_TimeSync_GnssToRcore_t tsValue;
    canFdFrame.can_id = 0x688;
    canFdFrame.len = sizeof(can_0x688_TimeSync_GnssToRcore_t);
    canFdFrame.flags = 0;

    // ref time
    uint64_t refTimeSec = gnssLocal / 1000;
    uint64_t refTimeNsec = (gnssLocal % 1000) * 1000 * 1000;

    // rtc time
    struct timespec rtcCurrentTime;
    int ret = timesyncGetCurrentTime(&rtcClock, &rtcCurrentTime);
    if (ret < 0) {
        MLOG_E("Get rtc clock current time failed");
        return false;
    }

    tsValue.rtc_time_sec = rtcCurrentTime.tv_sec;
    tsValue.rtc_time_nsec = rtcCurrentTime.tv_nsec;
    tsValue.ref_time_sec = refTimeSec;
    tsValue.ref_time_nsec = refTimeNsec;
    tsValue.Timesync_check_sum =
        CalculateChecksum((char*)&tsValue, sizeof(can_0x688_TimeSync_GnssToRcore_t) - sizeof(uint32_t));

    can_obj_ipc_matrix_middleware_h_t obj;
    ret = encode_can_0x688_rtc_time_sec(&obj, tsValue.rtc_time_sec);
    ret = encode_can_0x688_rtc_time_nsec(&obj, tsValue.rtc_time_nsec);
    ret = encode_can_0x688_ref_time_sec(&obj, tsValue.ref_time_sec);
    ret = encode_can_0x688_ref_time_nsec(&obj, tsValue.ref_time_nsec);
    ret = encode_can_0x688_Timesync_check_sum(&obj, tsValue.Timesync_check_sum);

    struct timeval tv;
    gettimeofday(&tv, NULL);
    uint64_t timestamp = (tv.tv_sec * 1000000) + tv.tv_usec;
    pack_ipc_matrix_middleware_h_message(&obj, canFdFrame.can_id, canFdFrame.data, canFdFrame.len, timestamp);

    if (ret != 0) {
        MLOG_E("SetGnssTime fail! encode can frame failed");
        return false;
    }
    return !!canIo->CanIo_Write(&canFdFrame);
}

bool GnssDevice::Reset() {
    if (!mIsRunning) {
        return false;
    }
    MLOG_I("Resetting gnss device");
    if (!GpioCtrl::SetGpio(GNSS_RST_PIN_IDX, 0)) {
        return false;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    if (!GpioCtrl::SetGpio(GNSS_RST_PIN_IDX, 1)) {
        return false;
    }
    MLOG_I("Reset gnss device finished");
    return true;
}

void GnssDevice::Terminate() {
    if (instance != nullptr) {
        dataStatics.reset();
        delete instance;
        instance = nullptr;
    }
}

GnssDevice::GnssDevice()
    : mTtyDevice(nullptr) {
}

GnssDevice::~GnssDevice() {
    mIsRunning = false;
    timesyncCloseClock(&rtcClock);
    // GpioCtrl::SetGpio(GNSS_PWR_GPIO_IDX, 0);
}

}  // namespace gnss
}  // namespace minieye
