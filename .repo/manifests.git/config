[core]
	repositoryFormatVersion = 1
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ********************:emb_basetech/gn/manifest.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[manifest]
	platform = auto
[extensions]
	preciousObjects = true
[branch "default"]
	remote = origin
	merge = refs/heads/d4l
[repo]
	existingprojectcount = 17
	newprojectcount = 0
[repo "syncstate.main"]
	synctime = 2025-09-02T06:48:59.328703+00:00
	version = 1
[repo "syncstate.sys"]
	argv = ['/home/<USER>/project/d4l/.repo/repo/main.py', '--repo-dir=/home/<USER>/project/d4l/.repo', '--wrapper-version=2.54', '--wrapper-path=/usr/bin/repo', '--', 'sync', '-c']
[repo "syncstate.options"]
	jobs = 8
	outermanifest = true
	jobsnetwork = 8
	jobscheckout = 8
	mpupdate = true
	currentbranchonly = true
	clonebundle = true
	retryfetches = 0
	prune = true
	repoverify = true
	quiet = false
	verbose = false
	interleaved = true
	repoupgraded = true
[repo "syncstate.remote.origin"]
	url = ********************:emb_basetech/gn/manifest.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[repo "syncstate.branch.default"]
	remote = origin
	merge = refs/heads/d4l
[repo "syncstate.repo"]
	existingprojectcount = 17
	newprojectcount = 0
