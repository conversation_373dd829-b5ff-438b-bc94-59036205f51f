231902aff26f9c76652237055bdf53e9a034f55a 231902aff26f9c76652237055bdf53e9a034f55a xujiesen <<EMAIL>> 1743594730 +0800
231902aff26f9c76652237055bdf53e9a034f55a c5afdfc80802d881c6e2a8000ddb4a5186dadf3f xujiesen <<EMAIL>> 1744189207 +0800	commit: feat(gnss/imu): 新增gnss、imu sdk
c5afdfc80802d881c6e2a8000ddb4a5186dadf3f c5afdfc80802d881c6e2a8000ddb4a5186dadf3f xujiesen <<EMAIL>> 1744192528 +0800	reset: moving to HEAD
c5afdfc80802d881c6e2a8000ddb4a5186dadf3f 8f24c74a6bc6c46cc99635409f927ddeafbd8ecf xuji<PERSON>n <<EMAIL>> 1744192533 +0800	pull: Fast-forward
8f24c74a6bc6c46cc99635409f927ddeafbd8ecf 0f78e8051070964b2143a3d5301c29b4068798e7 xujiesen <<EMAIL>> 1744192624 +0800	commit: feat(imu): ImuDevice改用std::thread，减少额外依赖
0f78e8051070964b2143a3d5301c29b4068798e7 daf48e545a612a5502bd3a27e916969083a917ba xujiesen <<EMAIL>> 1744198102 +0800	pull --tags j6e_app D4L: Fast-forward
daf48e545a612a5502bd3a27e916969083a917ba 8f77b8b1e7ef9c58c01ab091b62c825452ff7738 xujiesen <<EMAIL>> 1744203509 +0800	pull: Fast-forward
8f77b8b1e7ef9c58c01ab091b62c825452ff7738 75301cb8b5cab390e16a75024f4355eb594a7f3b xujiesen <<EMAIL>> 1744203519 +0800	commit: feat(gnss/imu): 更新gnss/imu sdk文档以及代码
75301cb8b5cab390e16a75024f4355eb594a7f3b c450c6ef9301eee1179ffc6903b00c8540889e1c xujiesen <<EMAIL>> 1744259044 +0800	commit: docs(imu/gnss): 更新测试文档和注释
c450c6ef9301eee1179ffc6903b00c8540889e1c a5a184c97d0957c0ef6658c8fc48e94650a63e6b xujiesen <<EMAIL>> 1744467814 +0800	pull: Fast-forward
a5a184c97d0957c0ef6658c8fc48e94650a63e6b 1e6e740074e943cb7cae1d50d6e48735e0a6d6d3 xujiesen <<EMAIL>> 1744468036 +0800	commit: feat(imu/gnss): 修正设备波特率、修复传输内存泄漏问题、修改imu sample
1e6e740074e943cb7cae1d50d6e48735e0a6d6d3 f79b2cb1a946955ff299b4a2766c484abcf9afac xujiesen <<EMAIL>> 1744544321 +0800	commit: feat(imu/gnss): 添加terminate()接口，添加test，优化电源操作
f79b2cb1a946955ff299b4a2766c484abcf9afac 4f7d72d2fd97b201eb7755aba064e551dcca33a7 xujiesen <<EMAIL>> 1744546663 +0800	commit: feat(gnss/imu): 优化gnss的reset接口，以及测试用例
4f7d72d2fd97b201eb7755aba064e551dcca33a7 d6d9ff91e11cb746e1443111e51cc614980fab33 xujiesen <<EMAIL>> 1744637236 +0800	commit: feat(gnss/imu): 测试用例补充结果和log，gnss补充接口描述，修复gpio打开失败的bug
d6d9ff91e11cb746e1443111e51cc614980fab33 97f93a5e6673bd79a0e97788e378d416d19d79a1 xujiesen <<EMAIL>> 1744637255 +0800	pull (start): checkout 97f93a5e6673bd79a0e97788e378d416d19d79a1
97f93a5e6673bd79a0e97788e378d416d19d79a1 d21e0c86a9beebad31f5522fa8a448b9c7d808ec xujiesen <<EMAIL>> 1744637255 +0800	pull (pick): feat(gnss/imu): 测试用例补充结果和log，gnss补充接口描述，修复gpio打开失败的bug
d21e0c86a9beebad31f5522fa8a448b9c7d808ec d21e0c86a9beebad31f5522fa8a448b9c7d808ec xujiesen <<EMAIL>> 1744637255 +0800	pull (finish): returning to refs/heads/d4l
d21e0c86a9beebad31f5522fa8a448b9c7d808ec 8bba886b1ab1a45808e33564c26deb283cc18cea xujiesen <<EMAIL>> 1744637603 +0800	commit: docs(imu/gnss): 添加产品协议文档
8bba886b1ab1a45808e33564c26deb283cc18cea 2c28d06ea273497fe83d949f6987c62cbb1c95ba xujiesen <<EMAIL>> 1744638023 +0800	pull: Fast-forward
2c28d06ea273497fe83d949f6987c62cbb1c95ba 5bc8aaa78e246589054bb763d7368b914e13d47a xujiesen <<EMAIL>> 1744638337 +0800	commit: docs(imu/gnss): 更新文档
5bc8aaa78e246589054bb763d7368b914e13d47a b0f5456a931a3610e61f8a20ac277afde427606f xujiesen <<EMAIL>> 1744641173 +0800	commit: feat(gnss): 优化电源操作
b0f5456a931a3610e61f8a20ac277afde427606f 7e41d79c3224773b61f37f96e14092fa8d005c51 xujiesen <<EMAIL>> 1744642118 +0800	merge 7e41d79c3224773b61f37f96e14092fa8d005c51: Fast-forward
7e41d79c3224773b61f37f96e14092fa8d005c51 fc5a9271ab0a9fab608bcf011a326c7a4ba525e5 xujiesen <<EMAIL>> 1744682650 +0800	pull: Fast-forward
fc5a9271ab0a9fab608bcf011a326c7a4ba525e5 922030f93d3bed2bd9a550e4966985635854c2af xujiesen <<EMAIL>> 1744684697 +0800	commit: feat(gnss/imu): 优化电源设置和初始化流程，更新文档
922030f93d3bed2bd9a550e4966985635854c2af bf7600089031056769368cb6d864f5b0c3570a28 xujiesen <<EMAIL>> 1744702653 +0800	merge bf7600089031056769368cb6d864f5b0c3570a28: Fast-forward
bf7600089031056769368cb6d864f5b0c3570a28 25cd4446e007527b5f7717964db8ff14d63b768b xujiesen <<EMAIL>> 1744706478 +0800	commit: fix(gnss): 修复sample解析
25cd4446e007527b5f7717964db8ff14d63b768b a74ad2698f6fbbee0b58b1d21449aa992ceb3297 xujiesen <<EMAIL>> 1745756611 +0800	merge a74ad2698f6fbbee0b58b1d21449aa992ceb3297: Fast-forward
a74ad2698f6fbbee0b58b1d21449aa992ceb3297 5d8aec6a5fc5783097342b17c07212d13514fbfa xujiesen <<EMAIL>> 1745808667 +0800	commit: refactor(gnss/imu): 优化电源操作，读写gpio使用linux文件操作；优化电源操作
5d8aec6a5fc5783097342b17c07212d13514fbfa f5c1a55b339208ad34a6aaf95c256635d869777c xujiesen <<EMAIL>> 1747036345 +0800	pull --tags j6e_app D4L: Fast-forward
f5c1a55b339208ad34a6aaf95c256635d869777c a6aadcf6f9849be78d007a63d24617d5f24d4447 xujiesen <<EMAIL>> 1747036584 +0800	commit: fix(gnss): 修复gnss sample解析出现崩溃问题
a6aadcf6f9849be78d007a63d24617d5f24d4447 d6c3dc8b7a0c80a3c85a571111c38183ddfdd02b xujiesen <<EMAIL>> 1748004172 +0800	pull: Fast-forward
d6c3dc8b7a0c80a3c85a571111c38183ddfdd02b 15812d3c1419ee067820bceb35a10a038eb53e38 xujiesen <<EMAIL>> 1748266673 +0800	commit: feat(gnss/imu): 添加升级工具
15812d3c1419ee067820bceb35a10a038eb53e38 f3a0d50f7bf3ba3fee12441cb4828edf131f713b xujiesen <<EMAIL>> 1748266680 +0800	pull (start): checkout f3a0d50f7bf3ba3fee12441cb4828edf131f713b
f3a0d50f7bf3ba3fee12441cb4828edf131f713b 5d80126ebcf54b55f7703812b949deb194383dcf xujiesen <<EMAIL>> 1748266680 +0800	pull (pick): feat(gnss/imu): 添加升级工具
5d80126ebcf54b55f7703812b949deb194383dcf 5d80126ebcf54b55f7703812b949deb194383dcf xujiesen <<EMAIL>> 1748266680 +0800	pull (finish): returning to refs/heads/d4l
5d80126ebcf54b55f7703812b949deb194383dcf 4caf85cc64f19ac8f034603a7613a3c0650d12bd xujiesen <<EMAIL>> 1748313786 +0800	commit: feat(gnss/imu): 添加升级工具说明，imu升级工具移除默认固件
4caf85cc64f19ac8f034603a7613a3c0650d12bd b5da6d6d0e461ad246b8d9af9a9384a51c71bd46 xujiesen <<EMAIL>> 1748606899 +0800	commit: fix(gnss/imu): 修正ota库拷贝路径
b5da6d6d0e461ad246b8d9af9a9384a51c71bd46 ee3e1f797a2ede30a4ac35ce3a77332c302b36cb xujiesen <<EMAIL>> 1748606905 +0800	pull (start): checkout ee3e1f797a2ede30a4ac35ce3a77332c302b36cb
ee3e1f797a2ede30a4ac35ce3a77332c302b36cb a4f9cdf306ffde5717e2a84f785a898abc61a815 xujiesen <<EMAIL>> 1748606905 +0800	pull (pick): fix(gnss/imu): 修正ota库拷贝路径
a4f9cdf306ffde5717e2a84f785a898abc61a815 a4f9cdf306ffde5717e2a84f785a898abc61a815 xujiesen <<EMAIL>> 1748606905 +0800	pull (finish): returning to refs/heads/d4l
a4f9cdf306ffde5717e2a84f785a898abc61a815 24c5b2b654040b8bd4d28948892bfd0ec4b83b2d xujiesen <<EMAIL>> 1748932587 +0800	rebase (start): checkout 24c5b2b654040b8bd4d28948892bfd0ec4b83b2d
24c5b2b654040b8bd4d28948892bfd0ec4b83b2d 24c5b2b654040b8bd4d28948892bfd0ec4b83b2d xujiesen <<EMAIL>> 1748932587 +0800	rebase (finish): returning to refs/heads/d4l
24c5b2b654040b8bd4d28948892bfd0ec4b83b2d 69552114ac2525b057d4d65b1d64cf6ee2cbda7a xujiesen <<EMAIL>> 1748950308 +0800	commit: merge(gnss/imu): 更新导远提供的otasdk：修复imu无数据时程序卡死的问题
69552114ac2525b057d4d65b1d64cf6ee2cbda7a ff44a4b2ad4dcf56e218c637b3c50d5c43d38ebf xujiesen <<EMAIL>> 1748950319 +0800	pull (start): checkout ff44a4b2ad4dcf56e218c637b3c50d5c43d38ebf
ff44a4b2ad4dcf56e218c637b3c50d5c43d38ebf 9774b40e05ec9a22743136a832ad18dd1ec24d47 xujiesen <<EMAIL>> 1748950319 +0800	pull (pick): merge(gnss/imu): 更新导远提供的otasdk：修复imu无数据时程序卡死的问题
9774b40e05ec9a22743136a832ad18dd1ec24d47 9774b40e05ec9a22743136a832ad18dd1ec24d47 xujiesen <<EMAIL>> 1748950319 +0800	pull (finish): returning to refs/heads/d4l
9774b40e05ec9a22743136a832ad18dd1ec24d47 7b55a4dbc0ae2d3465770a32e12a9f1d7efc5eb1 xujiesen <<EMAIL>> 1748950848 +0800	rebase (start): checkout 7b55a4dbc0ae2d3465770a32e12a9f1d7efc5eb1
7b55a4dbc0ae2d3465770a32e12a9f1d7efc5eb1 7b55a4dbc0ae2d3465770a32e12a9f1d7efc5eb1 xujiesen <<EMAIL>> 1748950848 +0800	rebase (finish): returning to refs/heads/d4l
7b55a4dbc0ae2d3465770a32e12a9f1d7efc5eb1 827d77232f96be5620026ba6ae03313e6e1f6fae xujiesen <<EMAIL>> 1748956248 +0800	commit: docs(gnss/imu): ota升级文档更新
827d77232f96be5620026ba6ae03313e6e1f6fae 8495395d3d1540cb7f75c0d5983cfb4b19aab48e xujiesen <<EMAIL>> 1750645382 +0800	merge 8495395d3d1540cb7f75c0d5983cfb4b19aab48e: Fast-forward
8495395d3d1540cb7f75c0d5983cfb4b19aab48e 898065fde5947747af0b6fdd9d678f02886e4a05 xujiesen <<EMAIL>> 1750659891 +0800	merge 898065fde5947747af0b6fdd9d678f02886e4a05: Fast-forward
898065fde5947747af0b6fdd9d678f02886e4a05 c166577752f797644a1218945db6997a4fab6634 xujiesen <<EMAIL>> 1751015772 +0800	merge c166577752f797644a1218945db6997a4fab6634: Fast-forward
c166577752f797644a1218945db6997a4fab6634 5186450084878d703fc7b776f3948c1c9067d806 xujiesen <<EMAIL>> 1751273663 +0800	pull --tags j6e_app D4L: Fast-forward
5186450084878d703fc7b776f3948c1c9067d806 5186450084878d703fc7b776f3948c1c9067d806 xujiesen <<EMAIL>> 1751509294 +0800	reset: moving to HEAD
5186450084878d703fc7b776f3948c1c9067d806 d4a9838c58212f79f4f36dc303eda1ae3066bb7e xujiesen <<EMAIL>> 1751509306 +0800	merge d4a9838c58212f79f4f36dc303eda1ae3066bb7e: Fast-forward
d4a9838c58212f79f4f36dc303eda1ae3066bb7e 6f76d7330d0bcff585b42b8a490d8b47fd7a0d71 xujiesen <<EMAIL>> 1751599109 +0800	commit: feat(module_diag_tools): 添加imu/gnss故障诊断组件
6f76d7330d0bcff585b42b8a490d8b47fd7a0d71 4ea78632ac51194ba9b45f03c918007ee2ef3a0c xujiesen <<EMAIL>> 1751599228 +0800	rebase (start): checkout 4ea78632ac51194ba9b45f03c918007ee2ef3a0c
4ea78632ac51194ba9b45f03c918007ee2ef3a0c 024d1e4f9163cd6706ee3be1398239ba6bf235cd xujiesen <<EMAIL>> 1751599228 +0800	rebase (pick): feat(module_diag_tools): 添加imu/gnss故障诊断组件
024d1e4f9163cd6706ee3be1398239ba6bf235cd 024d1e4f9163cd6706ee3be1398239ba6bf235cd xujiesen <<EMAIL>> 1751599228 +0800	rebase (finish): returning to refs/heads/d4l
024d1e4f9163cd6706ee3be1398239ba6bf235cd 3aed1e45045b5b6f51969d293cf5609e8eb946ea xujiesen <<EMAIL>> 1751612638 +0800	commit: fix(module_diag_tools): 修复编译错误，将module_diag_tools放到单独的目录
3aed1e45045b5b6f51969d293cf5609e8eb946ea 4790576ceb4d2aa6bdd95a00710001f1240864e1 xujiesen <<EMAIL>> 1752917193 +0800	commit: fix(module_diag_tools): 修复imu不上电时，会上报故障的问题
4790576ceb4d2aa6bdd95a00710001f1240864e1 3aed1e45045b5b6f51969d293cf5609e8eb946ea xujiesen <<EMAIL>> 1752921824 +0800	reset: moving to HEAD^
3aed1e45045b5b6f51969d293cf5609e8eb946ea b633675636c734e2784bbbf4a0dfa5e190c98acc xujiesen <<EMAIL>> 1752921845 +0800	commit: fix(module_diag_tools): 修复imu不上电时，会上报故障的问题
b633675636c734e2784bbbf4a0dfa5e190c98acc 7d2b4d0027616571344b315de547b76ecb40f8be xujiesen <<EMAIL>> 1752922199 +0800	rebase (start): checkout 7d2b4d0027616571344b315de547b76ecb40f8be
7d2b4d0027616571344b315de547b76ecb40f8be 925a4456f2d1db0e7bf4735464c6f3a116264774 xujiesen <<EMAIL>> 1752922199 +0800	rebase (pick): fix(module_diag_tools): 修复imu不上电时，会上报故障的问题
925a4456f2d1db0e7bf4735464c6f3a116264774 925a4456f2d1db0e7bf4735464c6f3a116264774 xujiesen <<EMAIL>> 1752922199 +0800	rebase (finish): returning to refs/heads/d4l
925a4456f2d1db0e7bf4735464c6f3a116264774 d3f71c75b09a0c81c24d8d78f7e007320b7fbdfc xujiesen <<EMAIL>> 1753418524 +0800	commit: feat(gnss/imu): 添加日志，打印日志使用alog库
d3f71c75b09a0c81c24d8d78f7e007320b7fbdfc 5a1ce2693a514b593792ee584e4cc42d80d37fb8 xujiesen <<EMAIL>> 1753418651 +0800	rebase (start): checkout 5a1ce2693a514b593792ee584e4cc42d80d37fb8
5a1ce2693a514b593792ee584e4cc42d80d37fb8 5fd1da93adbffdd0e6a579a373f6b9d945dbb2c2 xujiesen <<EMAIL>> 1753418651 +0800	rebase (pick): feat(gnss/imu): 添加日志，打印日志使用alog库
5fd1da93adbffdd0e6a579a373f6b9d945dbb2c2 5fd1da93adbffdd0e6a579a373f6b9d945dbb2c2 xujiesen <<EMAIL>> 1753418651 +0800	rebase (finish): returning to refs/heads/d4l
5fd1da93adbffdd0e6a579a373f6b9d945dbb2c2 bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 xujiesen <<EMAIL>> 1754463482 +0800	rebase (start): checkout bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72
bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 850dc2523144b59872eb7020b31529bf3a6ffcb2 xujiesen <<EMAIL>> 1754463482 +0800	rebase (pick): feat(gnss/imu): 添加日志，打印日志使用alog库
850dc2523144b59872eb7020b31529bf3a6ffcb2 850dc2523144b59872eb7020b31529bf3a6ffcb2 xujiesen <<EMAIL>> 1754463482 +0800	rebase (finish): returning to refs/heads/d4l
850dc2523144b59872eb7020b31529bf3a6ffcb2 bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 xujiesen <<EMAIL>> 1754463578 +0800	reset: moving to HEAD^
bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 671a2ad7576e1800e8b64f8404157640de357ba9 xujiesen <<EMAIL>> 1754464406 +0800	checkout: moving from d4l to xjs_dev_d4l
671a2ad7576e1800e8b64f8404157640de357ba9 d3f71c75b09a0c81c24d8d78f7e007320b7fbdfc xujiesen <<EMAIL>> 1754472766 +0800	reset: moving to HEAD^
d3f71c75b09a0c81c24d8d78f7e007320b7fbdfc 925a4456f2d1db0e7bf4735464c6f3a116264774 xujiesen <<EMAIL>> 1754472767 +0800	reset: moving to HEAD^
925a4456f2d1db0e7bf4735464c6f3a116264774 69d49b3d702a178aa88a0d5ab0c45490d5dfc31a xujiesen <<EMAIL>> 1754473352 +0800	commit: feat(gnss/imu): 添加日志，打印日志使用alog库
69d49b3d702a178aa88a0d5ab0c45490d5dfc31a 6b21477c3ae2c5876e707d3699bab7b018e104a1 xujiesen <<EMAIL>> 1754473377 +0800	checkout: moving from xjs_dev_d4l to master
6b21477c3ae2c5876e707d3699bab7b018e104a1 bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 xujiesen <<EMAIL>> 1754473386 +0800	checkout: moving from master to D4L
bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 xujiesen <<EMAIL>> 1754473422 +0800	checkout: moving from D4L to d4l
bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 8f8caec4987f19f4a1edc0abc465216c52747a96 xujiesen <<EMAIL>> 1754486579 +0800	commit: test(gnss/imu): 添加gnss/imu压力测试
8f8caec4987f19f4a1edc0abc465216c52747a96 b9ad58a5e637c30013e7c1ca4cf0569f2ebdd766 xujiesen <<EMAIL>> 1754914893 +0800	commit: feat(module_diag_tools): 添加mlog支持
b9ad58a5e637c30013e7c1ca4cf0569f2ebdd766 26241ac245fc5c5f98a9a3483255cf8eb8f4f0e7 xujiesen <<EMAIL>> 1754914956 +0800	commit: feat(module_diag_tools): 禁用gnss故障上报
26241ac245fc5c5f98a9a3483255cf8eb8f4f0e7 9b3e8d3a0e27a3ffacfd1e33782cc5dbef56ece2 xujiesen <<EMAIL>> 1754915036 +0800	checkout: moving from d4l to D4L_LR3
9b3e8d3a0e27a3ffacfd1e33782cc5dbef56ece2 592ed75df71c94504563728e763e48175d5b0929 xujiesen <<EMAIL>> 1754915045 +0800	cherry-pick: feat(module_diag_tools): 添加mlog支持
592ed75df71c94504563728e763e48175d5b0929 0853eea4ef645c5bfa2ec3f98591bbbee080c3c9 xujiesen <<EMAIL>> 1754915059 +0800	cherry-pick: feat(module_diag_tools): 禁用gnss故障上报
0853eea4ef645c5bfa2ec3f98591bbbee080c3c9 7c73f071f458a2721f973a7a032870f39fa84786 xujiesen <<EMAIL>> 1755078139 +0800	pull: Fast-forward
7c73f071f458a2721f973a7a032870f39fa84786 bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 xujiesen <<EMAIL>> 1755078144 +0800	checkout: moving from D4L_LR3 to D4L
bee8a2b62f3a0eaab3eeafc7acccaa59ea238f72 aa1e219f23fa39a14e5d72b0a49f996f0092002c xujiesen <<EMAIL>> 1755078146 +0800	pull: Fast-forward
aa1e219f23fa39a14e5d72b0a49f996f0092002c 796a1c59476ab3384dfe48cdee874b7a49ecba31 xujiesen <<EMAIL>> 1755520887 +0800	pull --tags j6e_app D4L: Fast-forward
796a1c59476ab3384dfe48cdee874b7a49ecba31 d6ad10d16a1dd190bfdbeeeae5cdd47087b9a467 xujiesen <<EMAIL>> 1755607941 +0800	revert: Revert "test(gnss/imu): 添加gnss/imu压力测试"
