defines =
include_dirs = -I../../applications/app/common/dbc/inc -I../../applications/app/common/timehal/include -I../../applications/app/gnss/include -I../../applications/app/gnss/src -I../../platform/D4L/build/sysroots/usr/minieye/include -I../../platform/D4L/build/sysroots/usr/hobot/include -I../../middleware/mlog/include -I../../middleware/system/core/libcppbase -I../../middleware/system/core/libcppbase/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4l/platform/D4L/build/sysroots -fPIC
cflags_c =
cflags_cc = -Wall -Wno-deprecated-declarations -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4l/platform/D4L/build/sysroots -fPIC
target_output_name = libgnss

build obj/applications/app/common/dbc/src/libgnss.IPC_matrix_Middleware.o: cc ../../applications/app/common/dbc/src/IPC_matrix_Middleware.c
build obj/applications/app/common/timehal/src/libgnss.minieye_time_hal.o: cxx ../../applications/app/common/timehal/src/minieye_time_hal.cpp
build obj/applications/app/gnss/src/libgnss.GnssDevice.o: cxx ../../applications/app/gnss/src/GnssDevice.cpp
build obj/applications/app/gnss/src/libgnss.TtyDevice.o: cxx ../../applications/app/gnss/src/TtyDevice.cpp
build obj/applications/app/gnss/src/libgnss.GpioCtrl.o: cxx ../../applications/app/gnss/src/GpioCtrl.cpp
build obj/applications/app/gnss/src/libgnss.CanIo.o: cxx ../../applications/app/gnss/src/CanIo.cpp

build ./libgnss.so: solink obj/applications/app/common/dbc/src/libgnss.IPC_matrix_Middleware.o obj/applications/app/common/timehal/src/libgnss.minieye_time_hal.o obj/applications/app/gnss/src/libgnss.GnssDevice.o obj/applications/app/gnss/src/libgnss.TtyDevice.o obj/applications/app/gnss/src/libgnss.GpioCtrl.o obj/applications/app/gnss/src/libgnss.CanIo.o ./libmlog.so ./libcppbase.so
  ldflags = -lhbtimesynchal -L/home/<USER>/project/d4l/out/D4L_debug -Wl,-rpath-link=/home/<USER>/project/d4l/out/D4L_debug -lpthread --sysroot=/home/<USER>/project/d4l/platform/D4L/build/sysroots -L/home/<USER>/project/d4l/platform/D4L/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4l/platform/D4L/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4l/platform/D4L/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4l/platform/D4L/build/sysroots/usr/hobot/lib
  libs =
  output_extension = .so
  output_dir = .
