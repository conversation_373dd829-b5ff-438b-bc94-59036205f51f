defines =
include_dirs = -I../../applications/app/common/timehal/include -I../../applications/app/imu/include -I../../applications/app/imu/src -I../../platform/D4L/build/sysroots/usr/minieye/include -I../../platform/D4L/build/sysroots/usr/hobot/include -I../../middleware/mlog/include -I../../middleware/system/core/libcppbase -I../../middleware/system/core/libcppbase/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4l/platform/D4L/build/sysroots -fPIC
cflags_cc = -Wall -Wno-deprecated-declarations -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O2 --sysroot=/home/<USER>/project/d4l/platform/D4L/build/sysroots -fPIC
target_output_name = libimu

build obj/applications/app/common/timehal/src/libimu.minieye_time_hal.o: cxx ../../applications/app/common/timehal/src/minieye_time_hal.cpp
build obj/applications/app/imu/src/libimu.ImuDevice.o: cxx ../../applications/app/imu/src/ImuDevice.cpp
build obj/applications/app/imu/src/libimu.TtyDevice.o: cxx ../../applications/app/imu/src/TtyDevice.cpp
build obj/applications/app/imu/src/libimu.GpioCtrl.o: cxx ../../applications/app/imu/src/GpioCtrl.cpp

build ./libimu.so: solink obj/applications/app/common/timehal/src/libimu.minieye_time_hal.o obj/applications/app/imu/src/libimu.ImuDevice.o obj/applications/app/imu/src/libimu.TtyDevice.o obj/applications/app/imu/src/libimu.GpioCtrl.o ./libmlog.so ./libcppbase.so
  ldflags = -lhbtimesynchal -L/home/<USER>/project/d4l/out/D4L_debug -Wl,-rpath-link=/home/<USER>/project/d4l/out/D4L_debug -lpthread --sysroot=/home/<USER>/project/d4l/platform/D4L/build/sysroots -L/home/<USER>/project/d4l/platform/D4L/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/project/d4l/platform/D4L/build/sysroots/usr/minieye/lib -L/home/<USER>/project/d4l/platform/D4L/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/project/d4l/platform/D4L/build/sysroots/usr/hobot/lib
  libs =
  output_extension = .so
  output_dir = .
