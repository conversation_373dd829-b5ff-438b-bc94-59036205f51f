#!/usr/bin/bash

# GPS_PPS_OUT[UART0_RTSN] (GPIO Index: 480+29=509)
echo 509 > /sys/class/gpio/export
echo in > /sys/class/gpio/gpio509/direction
# GPS_PWR_EN (GPIO Index: 423+15=438)
echo 438 > /sys/class/gpio/export
echo out > /sys/class/gpio/gpio438/direction
echo 0 > /sys/class/gpio/gpio438/value
# MAIN_GPS_RSTn[UART0_CTSN] (GPIO Index: 480+30=510)
echo 510 > /sys/class/gpio/export
echo out > /sys/class/gpio/gpio510/direction
echo 0 > /sys/class/gpio/gpio510/value
# GPS_DATA_EN[CAM_PINT] (GPIO Index: 439+2=441)
echo 441 > /sys/class/gpio/export
echo out > /sys/class/gpio/gpio441/direction
echo 0 > /sys/class/gpio/gpio441/value

sleep 2
# GPS_PWR_EN (GPIO Index: 423+15=438)
echo 1 > /sys/class/gpio/gpio438/value
sleep 0.2
# MAIN_GPS_RSTn[UART0_CTSN] (GPIO Index: 480+30=510)
echo 1 > /sys/class/gpio/gpio510/value
sleep 0.1
# GPS_DATA_EN[CAM_PINT] (GPIO Index: 439+2=441)
echo 1 > /sys/class/gpio/gpio441/value